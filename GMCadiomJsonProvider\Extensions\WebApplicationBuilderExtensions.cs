namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for WebApplicationBuilder to integrate GMCadiomJsonProvider with ASP.NET Core applications.
/// </summary>
public static class WebApplicationBuilderExtensions
{
    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder with the default Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJson(this WebApplicationBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder with the specified provider type.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="providerType">The JSON provider type to use.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJson(this WebApplicationBuilder builder, JsonProviderType providerType, Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = providerType };
        configure?.Invoke(config);

        // Register the JSON provider in the service collection
        builder.Services.AddJsonProvider(config);

        return builder;
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder with Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomMicrosoftJson(this WebApplicationBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder with Newtonsoft.Json provider.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomNewtonsoftJson(this WebApplicationBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Newtonsoft, configure);
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder using a fluent builder pattern.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJson(this WebApplicationBuilder builder, Action<IJsonProviderBuilder> configure)
    {
        builder.Services.AddJsonProvider(configure);
        return builder;
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder from a JSON configuration string.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJsonFromConfig(this WebApplicationBuilder builder, string jsonConfig)
    {
        builder.Services.AddJsonProviderFromJson(jsonConfig);
        return builder;
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder using a unified configuration.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJson(this WebApplicationBuilder builder, JsonConfiguration config)
    {
        builder.Services.AddJsonProvider(config);
        return builder;
    }

    /// <summary>
    /// Adds GMCadiomJsonProvider to the WebApplicationBuilder and configures it for both MVC and SignalR.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder instance.</param>
    /// <param name="providerType">The JSON provider type to use.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The WebApplicationBuilder for method chaining.</returns>
    public static WebApplicationBuilder AddGMCadiomJsonForWebApps(this WebApplicationBuilder builder, JsonProviderType providerType = JsonProviderType.Microsoft, Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = providerType };
        configure?.Invoke(config);

        // Register the JSON provider
        builder.Services.AddJsonProvider(config);

        // Configure for controllers/API
        builder.Services.AddControllers(options =>
        {
            // Add any controller-specific configuration here if needed
        });

        // Configure MVC JSON options
        if (providerType == JsonProviderType.Microsoft)
        {
            builder.Services.ConfigureHttpJsonOptions(options =>
            {
                var systemTextJsonOptions = config.ToSystemTextJsonOptions();
                options.SerializerOptions.WriteIndented = systemTextJsonOptions.WriteIndented;
                options.SerializerOptions.PropertyNameCaseInsensitive = systemTextJsonOptions.PropertyNameCaseInsensitive;
                options.SerializerOptions.AllowTrailingCommas = systemTextJsonOptions.AllowTrailingCommas;
                options.SerializerOptions.DefaultIgnoreCondition = systemTextJsonOptions.DefaultIgnoreCondition;
                options.SerializerOptions.PropertyNamingPolicy = systemTextJsonOptions.PropertyNamingPolicy;
                options.SerializerOptions.DictionaryKeyPolicy = systemTextJsonOptions.DictionaryKeyPolicy;
                options.SerializerOptions.IncludeFields = systemTextJsonOptions.IncludeFields;
                options.SerializerOptions.MaxDepth = systemTextJsonOptions.MaxDepth;
                options.SerializerOptions.ReadCommentHandling = systemTextJsonOptions.ReadCommentHandling;
                options.SerializerOptions.NumberHandling = systemTextJsonOptions.NumberHandling;
                options.SerializerOptions.Encoder = systemTextJsonOptions.Encoder;
            });
        }

        return builder;
    }
}
