﻿# GMCadiomJsonProvider

A flexible JSON serialization/deserialization provider library for .NET 8+ that supports both System.Text.Json and Newtonsoft.Json with fluent configuration, dependency injection, and factory patterns.

## Features

- **Multiple JSON Providers**: Support for both Microsoft System.Text.Json and Newtonsoft.Json
- **Fluent Configuration**: Builder pattern for easy and readable configuration
- **Factory Pattern**: Create providers using factory methods
- **Dependency Injection**: Built-in support for Microsoft.Extensions.DependencyInjection
- **Static Helpers**: Convenient static methods for quick usage
- **Async Support**: Full async/await support for all operations
- **Stream Support**: Direct serialization to/from streams
- **Type Safety**: Generic and non-generic overloads for flexibility
- **Comprehensive Configuration**: Extensive options for both providers

## Installation

```bash
dotnet add package GMCadiomJsonProvider
```

## Quick Start

### Using Static Helper Methods

```csharp
using GMCadiomJsonProvider;

// Serialize using default provider (Microsoft System.Text.Json)
var person = new Person { Name = "<PERSON>", Age = 30 };
var json = GMCadiomJson.Serialize(person);

// Deserialize
var deserializedPerson = GMCadiomJson.Deserialize<Person>(json);

// Async operations
var jsonAsync = await GMCadiomJson.SerializeAsync(person);
var personAsync = await GMCadiomJson.DeserializeAsync<Person>(jsonAsync);
```

### Using Builder Pattern

```csharp
using GMCadiomJsonProvider;

// Create a configured provider using fluent builder
var provider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft()
    .WithIndentation(true)
    .WithIgnoreNullValues(true)
    .WithCaseInsensitiveProperties(true)
    .Build();

var json = provider.Serialize(person);
```

### Using Factory Pattern

```csharp
using GMCadiomJsonProvider;

// Create providers using factory
var microsoftProvider = GMCadiomJson.Factory.CreateMicrosoftProvider();
var newtonsoftProvider = GMCadiomJson.Factory.CreateNewtonsoftProvider();

// With configuration
var configuredProvider = GMCadiomJson.CreateMicrosoft(options =>
{
    options.WriteIndented = true;
    options.PropertyNameCaseInsensitive = true;
});
```

## Dependency Injection

### Basic Registration

```csharp
using GMCadiomJsonProvider.Extensions;

// Register default provider (Microsoft System.Text.Json)
services.AddJsonProvider();

// Register specific provider
services.AddMicrosoftJsonProvider();
services.AddNewtonsoftJsonProvider();
```

### With Configuration

```csharp
// Configure Microsoft provider
services.AddMicrosoftJsonProvider(options =>
{
    options.WriteIndented = true;
    options.PropertyNameCaseInsensitive = true;
    options.IgnoreNullValues = true;
});

// Configure Newtonsoft provider
services.AddNewtonsoftJsonProvider(options =>
{
    options.WriteIndented = true;
    options.NullValueHandling = NullValueHandling.Ignore;
});
```

### Using Builder in DI

```csharp
services.AddJsonProvider(builder =>
{
    builder.UseNewtonsoft()
           .WithIndentation(true)
           .WithIgnoreNullValues(true);
});
```

### Injecting and Using

```csharp
public class MyService
{
    private readonly IJsonProvider _jsonProvider;

    public MyService(IJsonProvider jsonProvider)
    {
        _jsonProvider = jsonProvider;
    }

    public async Task<string> SerializeDataAsync<T>(T data)
    {
        return await _jsonProvider.SerializeAsync(data);
    }
}
```

## Configuration Options

### Common Options (Both Providers)

```csharp
var options = new SystemTextJsonOptions // or NewtonsoftJsonOptions
{
    WriteIndented = true,                    // Pretty-print JSON
    PropertyNameCaseInsensitive = true,      // Case-insensitive deserialization
    AllowTrailingCommas = true,              // Allow trailing commas in JSON
    IgnoreNullValues = true                  // Ignore null values during serialization
};
```

### System.Text.Json Specific Options

```csharp
var microsoftOptions = new SystemTextJsonOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    IncludeFields = true,
    MaxDepth = 128,
    ReadCommentHandling = true,
    NumberHandling = true,
    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
};

// Add custom converters
microsoftOptions.Converters.Add(new JsonStringEnumConverter());
```

### Newtonsoft.Json Specific Options

```csharp
var newtonsoftOptions = new NewtonsoftJsonOptions
{
    ContractResolver = new CamelCasePropertyNamesContractResolver(),
    DateFormatHandling = DateFormatHandling.IsoDateFormat,
    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
    NullValueHandling = NullValueHandling.Ignore,
    DefaultValueHandling = DefaultValueHandling.Ignore,
    MissingMemberHandling = MissingMemberHandling.Ignore,
    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
    TypeNameHandling = TypeNameHandling.None,
    MaxDepth = 128
};

// Add custom converters
newtonsoftOptions.Converters.Add(new StringEnumConverter());
```

## Unified Configuration System

The library provides a unified configuration system that allows you to define settings once and automatically map them to provider-specific configurations.

### Using Unified Configuration

```csharp
using GMCadiomJsonProvider.Configuration;

// Create a unified configuration
var config = new JsonConfiguration
{
    ProviderType = JsonProviderType.Microsoft,
    WriteIndented = true,
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    NullValueHandling = JsonNullValueHandling.Ignore,
    AllowComments = true,
    MaxDepth = 128
};

// Create provider from unified config
var provider = GMCadiomJson.CreateProvider(config);
```

### Configuration with Action

```csharp
var provider = GMCadiomJson.CreateProvider(config =>
{
    config.ProviderType = JsonProviderType.Newtonsoft;
    config.WriteIndented = true;
    config.PropertyNamingPolicy = JsonNamingPolicy.SnakeCase;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
    config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
});
```

### Builder with Unified Configuration

```csharp
var provider = GMCadiomJson.CreateBuilder()
    .UseConfiguration(config =>
    {
        config.ProviderType = JsonProviderType.Microsoft;
        config.WriteIndented = true;
        config.PropertyNamingPolicy = JsonNamingPolicy.KebabCase;
        config.NullValueHandling = JsonNullValueHandling.Ignore;
    })
    .Build();
```

### JSON Configuration Files

```csharp
// From JSON string
var jsonConfig = """
{
    "providerType": "Newtonsoft",
    "writeIndented": true,
    "propertyNamingPolicy": "CamelCase",
    "nullValueHandling": "Ignore"
}
""";

var provider = GMCadiomJson.CreateProviderFromJson(jsonConfig);

// Or using builder
var builderProvider = GMCadiomJson.CreateBuilder()
    .UseJsonConfiguration(jsonConfig)
    .Build();
```

### Configuration Serialization

```csharp
var config = new JsonConfiguration
{
    ProviderType = JsonProviderType.Microsoft,
    WriteIndented = true,
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
};

// Serialize to JSON
var configJson = config.ToJson();

// Deserialize from JSON
var deserializedConfig = JsonConfiguration.FromJson(configJson);
```

### Dependency Injection with Unified Configuration

```csharp
// Using configuration object
services.AddJsonProvider(new JsonConfiguration
{
    ProviderType = JsonProviderType.Microsoft,
    WriteIndented = false,
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
});

// Using configuration action
services.AddJsonProvider(config =>
{
    config.ProviderType = JsonProviderType.Newtonsoft;
    config.WriteIndented = true;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
});

// From JSON configuration
services.AddJsonProviderFromJson(jsonConfigString);
```

### Available Configuration Options

| Property | Description | Values |
|----------|-------------|---------|
| `ProviderType` | JSON provider to use | `Microsoft`, `Newtonsoft` |
| `WriteIndented` | Pretty-print JSON | `true`, `false` |
| `PropertyNamingPolicy` | Property naming convention | `Default`, `CamelCase`, `SnakeCase`, `KebabCase`, `PascalCase` |
| `NullValueHandling` | How to handle null values | `Include`, `Ignore`, `IgnoreWhenWriting` |
| `DateFormatHandling` | Date format in JSON | `IsoDateFormat`, `MicrosoftDateFormat`, `UnixTimeStamp` |
| `MissingMemberHandling` | Handle missing properties | `Ignore`, `Error` |
| `ReferenceLoopHandling` | Handle circular references | `Error`, `Ignore`, `Serialize` |
| `MaxDepth` | Maximum nesting depth | Integer (default: 64) |
| `AllowComments` | Allow JSON comments | `true`, `false` |
| `AllowTrailingCommas` | Allow trailing commas | `true`, `false` |
| `Culture` | Culture for formatting | Culture string (e.g., "en-US") |

## Advanced Usage

### Stream Operations

```csharp
// Serialize to stream
using var stream = new MemoryStream();
provider.SerializeToStream(stream, person);

// Deserialize from stream
stream.Position = 0;
var deserializedPerson = provider.DeserializeFromStream<Person>(stream);

// Async stream operations
using var asyncStream = new MemoryStream();
await provider.SerializeToStreamAsync(asyncStream, person);

asyncStream.Position = 0;
var asyncPerson = await provider.DeserializeFromStreamAsync<Person>(asyncStream);
```

### Non-Generic Operations

```csharp
// Serialize and deserialize using Type parameter
var json = provider.Serialize(person);
var deserializedObject = provider.Deserialize(json, typeof(Person));
var typedPerson = (Person)deserializedObject;

// Stream operations with Type
using var stream = new MemoryStream();
provider.SerializeToStream(stream, person);
stream.Position = 0;
var streamObject = provider.DeserializeFromStream(stream, typeof(Person));
```

### Provider Comparison

```csharp
// Create both providers for comparison
var microsoftProvider = GMCadiomJson.CreateMicrosoft();
var newtonsoftProvider = GMCadiomJson.CreateNewtonsoft();

// Serialize with both
var microsoftJson = microsoftProvider.Serialize(person);
var newtonsoftJson = newtonsoftProvider.Serialize(person);

Console.WriteLine($"Microsoft: {microsoftProvider.ProviderName}");
Console.WriteLine($"Newtonsoft: {newtonsoftProvider.ProviderName}");
```

## Best Practices

### 1. Choose the Right Provider

- **System.Text.Json (Microsoft)**:
  - Better performance and lower memory allocation
  - Built into .NET, no additional dependencies
  - Recommended for new applications
  - Limited customization compared to Newtonsoft

- **Newtonsoft.Json**:
  - More features and customization options
  - Better support for complex scenarios
  - Widely used in existing applications
  - Slightly slower than System.Text.Json

### 2. Configuration Guidelines

```csharp
// For APIs - use camelCase naming
var apiProvider = GMCadiomJson.CreateBuilder()
    .UseMicrosoft(options =>
    {
        options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.WriteIndented = false; // Compact for APIs
    })
    .Build();

// For configuration files - use indentation
var configProvider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft(options =>
    {
        options.WriteIndented = true;
        options.NullValueHandling = NullValueHandling.Ignore;
    })
    .Build();
```

### 3. Dependency Injection Best Practices

```csharp
// Register as singleton for better performance
services.AddSingleton<IJsonProvider>(serviceProvider =>
{
    return GMCadiomJson.CreateBuilder()
        .UseMicrosoft()
        .WithIndentation(false)
        .WithIgnoreNullValues(true)
        .Build();
});

// Or use the built-in extension methods
services.AddMicrosoftJsonProvider(options =>
{
    options.WriteIndented = false;
    options.IgnoreNullValues = true;
});
```

### 4. Error Handling

```csharp
try
{
    var json = provider.Serialize(complexObject);
    var deserialized = provider.Deserialize<ComplexObject>(json);
}
catch (JsonException ex)
{
    // Handle JSON-specific errors
    Console.WriteLine($"JSON Error: {ex.Message}");
}
catch (Exception ex)
{
    // Handle other errors
    Console.WriteLine($"General Error: {ex.Message}");
}
```

## ASP.NET Core Integration

GMCadiomJsonProvider provides seamless integration with ASP.NET Core through extension methods for various builders.

### MVC Integration

Configure JSON serialization for MVC controllers and API responses:

```csharp
using GMCadiomJsonProvider.Extensions;

// Basic MVC integration with default Microsoft provider
builder.Services.AddControllers()
    .AddGMCadiomJson(config =>
    {
        config.WriteIndented = false;
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.NullValueHandling = JsonNullValueHandling.Ignore;
    });

// Use Newtonsoft provider for MVC
builder.Services.AddControllers()
    .AddGMCadiomNewtonsoftJson(config =>
    {
        config.WriteIndented = true;
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
    });

// Using fluent builder pattern
builder.Services.AddControllers()
    .AddGMCadiomJson(jsonBuilder =>
    {
        jsonBuilder.UseMicrosoft()
                  .WithIndentation(false)
                  .WithCaseInsensitiveProperties(true)
                  .WithIgnoreNullValues(true);
    });
```

### SignalR Integration

Configure JSON serialization for SignalR hubs:

```csharp
// Basic SignalR integration
builder.Services.AddSignalR()
    .AddGMCadiomJsonProtocol(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.WriteIndented = false;
        config.PropertyNameCaseInsensitive = true;
    });

// Use Newtonsoft provider for SignalR
builder.Services.AddSignalR()
    .AddGMCadiomNewtonsoftJsonProtocol(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
    });

// From JSON configuration
var jsonConfig = """
{
    "providerType": "Microsoft",
    "writeIndented": false,
    "propertyNamingPolicy": "CamelCase",
    "nullValueHandling": "Ignore"
}
""";

builder.Services.AddSignalR()
    .AddGMCadiomJsonProtocolFromConfig(jsonConfig);
```

### SignalR Client Integration

Configure JSON serialization for SignalR client connections:

```csharp
using Microsoft.AspNetCore.SignalR.Client;
using GMCadiomJsonProvider.Extensions;

// Basic SignalR client integration
var connection = new HubConnectionBuilder()
    .WithUrl("https://example.com/chatHub")
    .AddGMCadiomJsonProtocol(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.WriteIndented = false;
        config.PropertyNameCaseInsensitive = true;
    })
    .Build();

// Use Newtonsoft provider for SignalR client
var connection2 = new HubConnectionBuilder()
    .WithUrl("https://example.com/chatHub")
    .AddGMCadiomNewtonsoftJsonProtocol(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
        config.NullValueHandling = JsonNullValueHandling.Ignore;
    })
    .Build();

// Using fluent builder pattern
var connection3 = new HubConnectionBuilder()
    .WithUrl("https://example.com/chatHub")
    .AddGMCadiomJsonProtocol(jsonBuilder =>
    {
        jsonBuilder.UseMicrosoft()
                  .WithIndentation(false)
                  .WithCaseInsensitiveProperties(true)
                  .WithIgnoreNullValues(true);
    })
    .Build();

// From JSON configuration
var jsonConfig = """
{
    "providerType": "Microsoft",
    "writeIndented": false,
    "propertyNamingPolicy": "CamelCase",
    "nullValueHandling": "Ignore"
}
""";

var connection4 = new HubConnectionBuilder()
    .WithUrl("https://example.com/chatHub")
    .AddGMCadiomJsonProtocolFromConfig(jsonConfig)
    .Build();

// Complete client example with event handlers
var chatConnection = new HubConnectionBuilder()
    .WithUrl("https://example.com/chatHub")
    .AddGMCadiomJsonProtocol(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.WriteIndented = false;
    })
    .Build();

// Set up event handlers
chatConnection.On<ChatMessage>("ReceiveMessage", (message) =>
{
    Console.WriteLine($"{message.User}: {message.Message}");
});

await chatConnection.StartAsync();

// Send messages with automatic JSON serialization
await chatConnection.InvokeAsync("SendMessage", new ChatMessage
{
    User = "John",
    Message = "Hello World!",
    Timestamp = DateTime.UtcNow
});
```

### WebApplicationBuilder Integration

Configure JSON provider at the application level:

```csharp
// Basic integration
var builder = WebApplication.CreateBuilder();
builder.AddGMCadiomJson(config =>
{
    config.WriteIndented = true;
    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
});

// Environment-specific configuration
if (builder.Environment.IsDevelopment())
{
    builder.AddGMCadiomJson(config =>
    {
        config.WriteIndented = true; // Pretty print in development
    });
}
else
{
    builder.AddGMCadiomJson(config =>
    {
        config.WriteIndented = false; // Compact in production
        config.NullValueHandling = JsonNullValueHandling.Ignore;
    });
}

// Configure for web applications (includes MVC setup)
builder.AddGMCadiomJsonForWebApps(JsonProviderType.Microsoft, config =>
{
    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.WriteIndented = false;
});
```

### HTTP Client Integration

Configure JSON serialization for HTTP clients:

```csharp
// Basic HTTP client integration
builder.Services.AddHttpClient("ApiClient")
    .AddGMCadiomJson(config =>
    {
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        config.NullValueHandling = JsonNullValueHandling.Ignore;
    });

// Multiple clients with different configurations
builder.Services.AddHttpClient("InternalApi")
    .AddGMCadiomMicrosoftJson(config =>
    {
        config.WriteIndented = false;
        config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    });

builder.Services.AddHttpClient("ExternalApi")
    .AddGMCadiomNewtonsoftJson(config =>
    {
        config.PropertyNameCaseInsensitive = true;
        config.MissingMemberHandling = JsonMissingMemberHandling.Ignore;
    });

// Using the HTTP client with JSON extensions
public class ApiService
{
    private readonly HttpClient _httpClient;
    private readonly IJsonProvider _jsonProvider;

    public ApiService(HttpClient httpClient, IJsonProvider jsonProvider)
    {
        _httpClient = httpClient;
        _jsonProvider = jsonProvider;
    }

    public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
    {
        var response = await _httpClient.PostAsJsonAsync(endpoint, data, _jsonProvider);
        return await response.ReadFromJsonAsync<ApiResponse<T>>(_jsonProvider);
    }
}
```

### Complete Example

```csharp
var builder = WebApplication.CreateBuilder();

// Add GMCadiomJsonProvider to the application
builder.AddGMCadiomJson(config =>
{
    config.ProviderType = JsonProviderType.Microsoft;
    config.WriteIndented = builder.Environment.IsDevelopment();
    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
});

// Configure MVC with the same provider
builder.Services.AddControllers()
    .AddGMCadiomMicrosoftJson();

// Configure SignalR with the same provider
builder.Services.AddSignalR()
    .AddGMCadiomMicrosoftJsonProtocol();

// Configure HTTP clients
builder.Services.AddHttpClient("ApiClient")
    .AddGMCadiomMicrosoftJson();

var app = builder.Build();

app.UseRouting();
app.MapControllers();
app.MapHub<ChatHub>("/chatHub");

app.Run();
```

## Custom JSON Attributes

GMCadiomJsonProvider provides custom attributes that work with both Microsoft System.Text.Json and Newtonsoft.Json providers, allowing you to write provider-agnostic code.

### Available Attributes

#### GMCadiomJsonInclude
Controls when a property should be included in JSON serialization:

```csharp
public class Person
{
    [GMCadiomJsonInclude(JsonIncludeCondition.Always)]
    public int Id { get; set; }

    [GMCadiomJsonInclude(JsonIncludeCondition.WhenNotNull)]
    public string? Email { get; set; }

    [GMCadiomJsonInclude(JsonIncludeCondition.WhenNotDefault)]
    public int Age { get; set; }
}
```

#### GMCadiomJsonIgnore
Controls when a property should be ignored during JSON serialization:

```csharp
public class Person
{
    [GMCadiomJsonIgnore(JsonIgnoreCondition.Always)]
    public string InternalId { get; set; }

    [GMCadiomJsonIgnore(JsonIgnoreCondition.WhenWriting)]
    public string SecretData { get; set; }

    [GMCadiomJsonIgnore(JsonIgnoreCondition.WhenNull)]
    public string? OptionalField { get; set; }
}
```

#### GMCadiomJsonPropertyName
Specifies the JSON property name:

```csharp
public class Person
{
    [GMCadiomJsonPropertyName("full_name")]
    public string FullName { get; set; }

    [GMCadiomJsonPropertyName("email_address")]
    public string Email { get; set; }
}
```

#### GMCadiomJsonConstructor
Specifies which constructor to use for deserialization:

```csharp
public class Person
{
    public string FirstName { get; set; }
    public string LastName { get; set; }

    public Person() { }

    [GMCadiomJsonConstructor]
    public Person(string firstName, string lastName)
    {
        FirstName = firstName;
        LastName = lastName;
    }
}
```

#### GMCadiomJsonConverter
Specifies a custom converter for a property or type:

```csharp
public class Person
{
    [GMCadiomJsonConverter(typeof(CustomDateConverter))]
    public DateTime BirthDate { get; set; }
}
```

### Using Custom Attributes

Enable custom attribute processing when creating providers:

```csharp
// Enable for all types with custom attributes
var provider = GMCadiomJson.CreateBuilder()
    .UseMicrosoft()
    .WithCustomAttributes(true)
    .Build();

// Enable for specific types only
var provider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft()
    .WithCustomAttributes(true)
    .WithCustomAttributeTypes(typeof(Person), typeof(Address))
    .Build();

// Using configuration
var config = new JsonConfiguration
{
    ProviderType = JsonProviderType.Microsoft,
    ProcessCustomAttributes = true,
    CustomAttributeTypes = new[] { typeof(Person) }
};

var provider = GMCadiomJson.CreateProvider(config);
```

### Complete Example

```csharp
[GMCadiomJsonConverter(typeof(PersonConverter))]
public class Person
{
    [GMCadiomJsonInclude(JsonIncludeCondition.Always)]
    public int Id { get; set; }

    [GMCadiomJsonPropertyName("full_name")]
    public string FullName { get; set; }

    [GMCadiomJsonPropertyName("email_address")]
    [GMCadiomJsonInclude(JsonIncludeCondition.WhenNotNull)]
    public string? Email { get; set; }

    [GMCadiomJsonIgnore(JsonIgnoreCondition.Always)]
    public string InternalId { get; set; }

    [GMCadiomJsonIgnore(JsonIgnoreCondition.WhenWriting)]
    public string SecretData { get; set; }

    [GMCadiomJsonConstructor]
    public Person(int id, string fullName)
    {
        Id = id;
        FullName = fullName;
    }

    public Person() { }
}

// Usage
var provider = GMCadiomJson.CreateBuilder()
    .UseMicrosoft()
    .WithCustomAttributes(true)
    .WithCustomAttributeTypes(typeof(Person))
    .Build();

var person = new Person(123, "John Doe")
{
    Email = "<EMAIL>",
    InternalId = "INTERNAL_123",
    SecretData = "secret"
};

var json = provider.Serialize(person);
// Output: {"Id":123,"full_name":"John Doe","email_address":"<EMAIL>"}

var deserialized = provider.Deserialize<Person>(json);
```

## API Reference

### IJsonProvider Interface

```csharp
public interface IJsonProvider
{
    JsonProviderType ProviderType { get; }
    string ProviderName { get; }

    // Synchronous methods
    string Serialize<T>(T value);
    T? Deserialize<T>(string json);
    object? Deserialize(string json, Type type);

    // Asynchronous methods
    Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default);
    Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default);

    // Stream methods
    void SerializeToStream<T>(Stream stream, T value);
    T? DeserializeFromStream<T>(Stream stream);
    object? DeserializeFromStream(Stream stream, Type type);

    // Async stream methods
    Task SerializeToStreamAsync<T>(Stream stream, T value, CancellationToken cancellationToken = default);
    Task<T?> DeserializeFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken = default);
}
```

### Static Helper Methods

```csharp
public static class GMCadiomJson
{
    // Factory and builders
    public static IJsonProviderFactory Factory { get; }
    public static IJsonProvider Default { get; }
    public static IJsonProviderBuilder CreateBuilder();

    // Provider creation
    public static IJsonProvider CreateDefault();
    public static IJsonProvider CreateMicrosoft(Action<SystemTextJsonOptions>? configure = null);
    public static IJsonProvider CreateNewtonsoft(Action<NewtonsoftJsonOptions>? configure = null);
    public static IJsonProvider CreateProvider(JsonProviderType providerType);

    // Convenience methods using default provider
    public static string Serialize<T>(T value);
    public static Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default);
    public static T? Deserialize<T>(string json);
    public static Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default);
    public static object? Deserialize(string json, Type type);
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.