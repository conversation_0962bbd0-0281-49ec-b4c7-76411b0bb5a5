using GMCadiomJsonProvider.Attributes.Processing;

namespace GMCadiomJsonProvider.Implementation;

/// <summary>
/// JSON provider implementation using System.Text.Json.
/// </summary>
public class SystemTextJsonProvider : IJsonProvider
{
    private readonly System.Text.Json.JsonSerializerOptions _options;

    /// <summary>
    /// Initializes a new instance of the <see cref="SystemTextJsonProvider"/> class.
    /// </summary>
    /// <param name="configuration">The unified JSON configuration.</param>
    public SystemTextJsonProvider(JsonConfiguration? configuration = null)
    {
        var config = configuration ?? new JsonConfiguration { ProviderType = JsonProviderType.Microsoft };
        _options = config.ToSystemTextJsonOptions();

        // Process custom attributes if enabled
        if (config.ProcessCustomAttributes)
        {
            var attributeProcessor = new AttributeProcessor();
            attributeProcessor.ProcessAttributesForSystemTextJson(_options, config.CustomAttributeTypes);
        }
    }

    /// <summary>
    /// Gets the type of the JSON provider.
    /// </summary>
    public JsonProviderType ProviderType => JsonProviderType.Microsoft;

    /// <summary>
    /// Gets the name of the JSON provider.
    /// </summary>
    public string ProviderName => "Microsoft";

    /// <summary>
    /// Serializes an object to a JSON string.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <returns>A JSON string representation of the object.</returns>
    public string Serialize<T>(T value)
    {
        return System.Text.Json.JsonSerializer.Serialize(value, _options);
    }

    /// <summary>
    /// Serializes an object to a JSON string asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a JSON string representation of the object.</returns>
    public Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(System.Text.Json.JsonSerializer.Serialize(value, _options));
    }

    /// <summary>
    /// Serializes an object to a stream.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    public void SerializeToStream<T>(Stream stream, T value)
    {
        System.Text.Json.JsonSerializer.Serialize(stream, value, _options);
    }

    /// <summary>
    /// Serializes an object to a stream asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task SerializeToStreamAsync<T>(Stream stream, T value, CancellationToken cancellationToken = default)
    {
        await System.Text.Json.JsonSerializer.SerializeAsync(stream, value, _options, cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <returns>The deserialized object.</returns>
    public T? Deserialize<T>(string json)
    {
        return System.Text.Json.JsonSerializer.Deserialize<T>(json, _options);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    public Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(System.Text.Json.JsonSerializer.Deserialize<T>(json, _options));
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <returns>The deserialized object.</returns>
    public T? DeserializeFromStream<T>(Stream stream)
    {
        return System.Text.Json.JsonSerializer.Deserialize<T>(stream, _options);
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    public async Task<T?> DeserializeFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken = default)
    {
        return await System.Text.Json.JsonSerializer.DeserializeAsync<T>(stream, _options, cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    public object? Deserialize(string json, Type type)
    {
        return System.Text.Json.JsonSerializer.Deserialize(json, type, _options);
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    public object? DeserializeFromStream(Stream stream, Type type)
    {
        return System.Text.Json.JsonSerializer.Deserialize(stream, type, _options);
    }
}
