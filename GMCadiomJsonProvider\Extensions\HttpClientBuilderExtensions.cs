namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for IHttpClientBuilder to integrate GMCadiomJsonProvider with HTTP clients.
/// </summary>
public static class HttpClientBuilderExtensions
{
    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider for JSON serialization with the default Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomJson(this IHttpClientBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider for JSON serialization with the specified provider type.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="providerType">The JSON provider type to use.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomJson(this IHttpClientBuilder builder, JsonProviderType providerType, Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = providerType };
        configure?.Invoke(config);

        // Register the JSON provider if not already registered
        builder.Services.AddJsonProvider(config);

        // Add a delegating handler that provides JSON serialization capabilities
        builder.AddHttpMessageHandler(serviceProvider =>
        {
            var jsonProvider = serviceProvider.GetRequiredService<IJsonProvider>();
            return new JsonHttpMessageHandler(jsonProvider);
        });

        return builder;
    }

    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider with Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomMicrosoftJson(this IHttpClientBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider with Newtonsoft.Json provider.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomNewtonsoftJson(this IHttpClientBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Newtonsoft, configure);
    }

    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider using a fluent builder pattern.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomJson(this IHttpClientBuilder builder, Action<IJsonProviderBuilder> configure)
    {
        builder.Services.AddJsonProvider(configure);

        builder.AddHttpMessageHandler(serviceProvider =>
        {
            var jsonProvider = serviceProvider.GetRequiredService<IJsonProvider>();
            return new JsonHttpMessageHandler(jsonProvider);
        });

        return builder;
    }

    /// <summary>
    /// Configures the HTTP client to use GMCadiomJsonProvider from a JSON configuration string.
    /// </summary>
    /// <param name="builder">The IHttpClientBuilder instance.</param>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The IHttpClientBuilder for method chaining.</returns>
    public static IHttpClientBuilder AddGMCadiomJsonFromConfig(this IHttpClientBuilder builder, string jsonConfig)
    {
        builder.Services.AddJsonProviderFromJson(jsonConfig);

        builder.AddHttpMessageHandler(serviceProvider =>
        {
            var jsonProvider = serviceProvider.GetRequiredService<IJsonProvider>();
            return new JsonHttpMessageHandler(jsonProvider);
        });

        return builder;
    }
}

/// <summary>
/// HTTP message handler that provides JSON serialization capabilities using GMCadiomJsonProvider.
/// </summary>
internal class JsonHttpMessageHandler : DelegatingHandler
{
    private readonly IJsonProvider _jsonProvider;

    public JsonHttpMessageHandler(IJsonProvider jsonProvider)
    {
        _jsonProvider = jsonProvider ?? throw new ArgumentNullException(nameof(jsonProvider));
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Process request content if it's JSON
        if (request.Content != null && IsJsonContent(request.Content))
        {
            // You could add request transformation logic here if needed
        }

        var response = await base.SendAsync(request, cancellationToken);

        // Process response content if it's JSON
        if (response.Content != null && IsJsonContent(response.Content))
        {
            // You could add response transformation logic here if needed
        }

        return response;
    }

    private static bool IsJsonContent(HttpContent content)
    {
        return content.Headers.ContentType?.MediaType?.Contains("json", StringComparison.OrdinalIgnoreCase) == true;
    }
}

/// <summary>
/// Extension methods for HttpClient to work with GMCadiomJsonProvider.
/// </summary>
public static class HttpClientJsonExtensions
{
    /// <summary>
    /// Sends a POST request with JSON content using the specified JSON provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="client">The HttpClient instance.</param>
    /// <param name="requestUri">The request URI.</param>
    /// <param name="value">The object to serialize as JSON.</param>
    /// <param name="jsonProvider">The JSON provider to use for serialization.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The HTTP response message.</returns>
    public static async Task<HttpResponseMessage> PostAsJsonAsync<T>(this HttpClient client, string requestUri, T value, IJsonProvider jsonProvider, CancellationToken cancellationToken = default)
    {
        var json = await jsonProvider.SerializeAsync(value);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        return await client.PostAsync(requestUri, content, cancellationToken);
    }

    /// <summary>
    /// Sends a PUT request with JSON content using the specified JSON provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="client">The HttpClient instance.</param>
    /// <param name="requestUri">The request URI.</param>
    /// <param name="value">The object to serialize as JSON.</param>
    /// <param name="jsonProvider">The JSON provider to use for serialization.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The HTTP response message.</returns>
    public static async Task<HttpResponseMessage> PutAsJsonAsync<T>(this HttpClient client, string requestUri, T value, IJsonProvider jsonProvider, CancellationToken cancellationToken = default)
    {
        var json = await jsonProvider.SerializeAsync(value);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        return await client.PutAsync(requestUri, content, cancellationToken);
    }

    /// <summary>
    /// Reads the response content as JSON and deserializes it using the specified JSON provider.
    /// </summary>
    /// <typeparam name="T">The type to deserialize to.</typeparam>
    /// <param name="response">The HTTP response message.</param>
    /// <param name="jsonProvider">The JSON provider to use for deserialization.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The deserialized object.</returns>
    public static async Task<T?> ReadFromJsonAsync<T>(this HttpResponseMessage response, IJsonProvider jsonProvider, CancellationToken cancellationToken = default)
    {
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return await jsonProvider.DeserializeAsync<T>(json);
    }
}
