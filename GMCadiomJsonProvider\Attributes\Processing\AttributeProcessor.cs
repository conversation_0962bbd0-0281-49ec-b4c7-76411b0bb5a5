using System.Reflection;
using GMCadiomJsonProvider.Converters;

namespace GMCadiomJsonProvider.Attributes.Processing;

/// <summary>
/// Default implementation of attribute processor for custom JSON attributes.
/// </summary>
public class AttributeProcessor : IAttributeProcessor
{
    private readonly HashSet<Type> _processedTypes = new();
    private readonly object _lock = new();

    /// <summary>
    /// Processes custom attributes and configures System.Text.Json options accordingly.
    /// </summary>
    /// <param name="options">The JsonSerializerOptions to configure.</param>
    /// <param name="types">The types to process attributes for. If null, processes all loaded types.</param>
    public void ProcessAttributesForSystemTextJson(System.Text.Json.JsonSerializerOptions options, IEnumerable<Type>? types = null)
    {
        lock (_lock)
        {
            var typesToProcess = types ?? GetTypesWithCustomAttributes();
            
            foreach (var type in typesToProcess)
            {
                if (_processedTypes.Contains(type))
                    continue;

                ProcessTypeForSystemTextJson(type, options);
                _processedTypes.Add(type);
            }
        }
    }

    /// <summary>
    /// Processes custom attributes and configures Newtonsoft.Json settings accordingly.
    /// </summary>
    /// <param name="settings">The JsonSerializerSettings to configure.</param>
    /// <param name="types">The types to process attributes for. If null, processes all loaded types.</param>
    public void ProcessAttributesForNewtonsoft(Newtonsoft.Json.JsonSerializerSettings settings, IEnumerable<Type>? types = null)
    {
        lock (_lock)
        {
            var typesToProcess = types ?? GetTypesWithCustomAttributes();
            
            foreach (var type in typesToProcess)
            {
                ProcessTypeForNewtonsoft(type, settings);
            }
        }
    }

    /// <summary>
    /// Gets all types that have custom JSON attributes applied.
    /// </summary>
    /// <returns>An enumerable of types with custom attributes.</returns>
    public IEnumerable<Type> GetTypesWithCustomAttributes()
    {
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();
        var typesWithAttributes = new List<Type>();

        foreach (var assembly in assemblies)
        {
            try
            {
                var types = assembly.GetTypes();
                foreach (var type in types)
                {
                    if (HasCustomAttributes(type))
                    {
                        typesWithAttributes.Add(type);
                    }
                }
            }
            catch (ReflectionTypeLoadException)
            {
                // Skip assemblies that can't be loaded
                continue;
            }
        }

        return typesWithAttributes;
    }

    /// <summary>
    /// Checks if a type has any custom JSON attributes.
    /// </summary>
    /// <param name="type">The type to check.</param>
    /// <returns>True if the type has custom attributes; otherwise, false.</returns>
    public bool HasCustomAttributes(Type type)
    {
        // Check type-level attributes
        if (type.GetCustomAttributes<GMCadiomJsonConverterAttribute>().Any())
            return true;

        // Check constructor attributes
        var constructors = type.GetConstructors();
        if (constructors.Any(c => c.GetCustomAttributes<GMCadiomJsonConstructorAttribute>().Any()))
            return true;

        // Check property and field attributes
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (HasCustomJsonAttributes(property))
                return true;
        }

        foreach (var field in fields)
        {
            if (HasCustomJsonAttributes(field))
                return true;
        }

        return false;
    }

    private void ProcessTypeForSystemTextJson(Type type, System.Text.Json.JsonSerializerOptions options)
    {
        // Add custom contract resolver if not already present
        var contractResolver = options.TypeInfoResolver as GMCadiomSystemTextJsonContractResolver;
        if (contractResolver == null)
        {
            contractResolver = new GMCadiomSystemTextJsonContractResolver();
            options.TypeInfoResolver = contractResolver;
        }

        contractResolver.AddTypeWithCustomAttributes(type);
    }

    private void ProcessTypeForNewtonsoft(Type type, Newtonsoft.Json.JsonSerializerSettings settings)
    {
        // Add custom contract resolver if not already present
        var contractResolver = settings.ContractResolver as GMCadiomNewtonsoftContractResolver;
        if (contractResolver == null)
        {
            contractResolver = new GMCadiomNewtonsoftContractResolver();
            settings.ContractResolver = contractResolver;
        }

        contractResolver.AddTypeWithCustomAttributes(type);
    }

    private static bool HasCustomJsonAttributes(MemberInfo member)
    {
        return member.GetCustomAttributes<GMCadiomJsonIncludeAttribute>().Any() ||
               member.GetCustomAttributes<GMCadiomJsonIgnoreAttribute>().Any() ||
               member.GetCustomAttributes<GMCadiomJsonPropertyNameAttribute>().Any() ||
               member.GetCustomAttributes<GMCadiomJsonConverterAttribute>().Any();
    }
}
