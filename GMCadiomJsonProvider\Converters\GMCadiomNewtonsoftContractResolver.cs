using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using GMCadiomJsonProvider.Attributes;

namespace GMCadiomJsonProvider.Converters;

/// <summary>
/// Custom contract resolver for Newtonsoft.Json that processes GMCadiom custom attributes.
/// </summary>
public class GMCadiomNewtonsoftContractResolver : DefaultContractResolver
{
    private readonly HashSet<Type> _typesWithCustomAttributes = new();
    private readonly object _lock = new();

    /// <summary>
    /// Adds a type that has custom attributes to be processed.
    /// </summary>
    /// <param name="type">The type with custom attributes.</param>
    public void AddTypeWithCustomAttributes(Type type)
    {
        lock (_lock)
        {
            _typesWithCustomAttributes.Add(type);
        }
    }

    /// <summary>
    /// Creates a JsonObjectContract for the given type.
    /// </summary>
    /// <param name="objectType">The type to create a contract for.</param>
    /// <returns>A JsonObjectContract for the type.</returns>
    protected override JsonObjectContract CreateObjectContract(Type objectType)
    {
        var contract = base.CreateObjectContract(objectType);

        lock (_lock)
        {
            if (_typesWithCustomAttributes.Contains(objectType))
            {
                ProcessCustomAttributes(contract, objectType);
            }
        }

        return contract;
    }

    /// <summary>
    /// Creates a JsonProperty for the given member.
    /// </summary>
    /// <param name="member">The member to create a property for.</param>
    /// <param name="memberSerialization">The member serialization mode.</param>
    /// <returns>A JsonProperty for the member.</returns>
    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        var property = base.CreateProperty(member, memberSerialization);

        ProcessMemberAttributes(property, member);

        return property;
    }

    private void ProcessCustomAttributes(JsonObjectContract contract, Type objectType)
    {
        // Process constructor attributes
        ProcessConstructorAttributes(contract, objectType);
    }

    private void ProcessConstructorAttributes(JsonObjectContract contract, Type objectType)
    {
        var constructors = objectType.GetConstructors();
        var markedConstructor = constructors.FirstOrDefault(c => 
            c.GetCustomAttribute<GMCadiomJsonConstructorAttribute>() != null);

        if (markedConstructor != null)
        {
            var parameters = markedConstructor.GetParameters();
            if (parameters.Length > 0)
            {
                // Set the constructor for parameterized constructors
                contract.CreatorParameters.Clear();
                
                foreach (var param in parameters)
                {
                    var jsonProperty = new JsonProperty
                    {
                        PropertyName = param.Name,
                        PropertyType = param.ParameterType,
                        Readable = false,
                        Writable = true
                    };
                    
                    contract.CreatorParameters.Add(jsonProperty);
                }
            }
        }
    }

    private void ProcessMemberAttributes(JsonProperty property, MemberInfo member)
    {
        // Process GMCadiomJsonPropertyName attribute
        var propertyNameAttr = member.GetCustomAttribute<GMCadiomJsonPropertyNameAttribute>();
        if (propertyNameAttr != null)
        {
            property.PropertyName = propertyNameAttr.Name;
        }

        // Process GMCadiomJsonIgnore attribute
        var ignoreAttr = member.GetCustomAttribute<GMCadiomJsonIgnoreAttribute>();
        if (ignoreAttr != null)
        {
            switch (ignoreAttr.Condition)
            {
                case GMCadiomJsonIgnoreCondition.Always:
                    property.Ignored = true;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenWriting:
                    property.Readable = false;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenReading:
                    property.Writable = false;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenNull:
                    property.NullValueHandling = NullValueHandling.Ignore;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenDefault:
                    property.DefaultValueHandling = DefaultValueHandling.Ignore;
                    break;
            }
        }

        // Process GMCadiomJsonInclude attribute
        var includeAttr = member.GetCustomAttribute<GMCadiomJsonIncludeAttribute>();
        if (includeAttr != null)
        {
            switch (includeAttr.Condition)
            {
                case GMCadiomJsonIncludeCondition.Always:
                    property.Ignored = false;
                    property.NullValueHandling = NullValueHandling.Include;
                    property.DefaultValueHandling = DefaultValueHandling.Include;
                    break;
                case GMCadiomJsonIncludeCondition.WhenNotNull:
                    property.NullValueHandling = NullValueHandling.Ignore;
                    break;
                case GMCadiomJsonIncludeCondition.WhenNotDefault:
                    property.DefaultValueHandling = DefaultValueHandling.Ignore;
                    break;
                case GMCadiomJsonIncludeCondition.Never:
                    property.Ignored = true;
                    break;
            }
        }

        // Process GMCadiomJsonConverter attribute
        var converterAttr = member.GetCustomAttribute<GMCadiomJsonConverterAttribute>();
        if (converterAttr != null)
        {
            try
            {
                var converter = converterAttr.Parameters?.Length > 0
                    ? Activator.CreateInstance(converterAttr.ConverterType, converterAttr.Parameters)
                    : Activator.CreateInstance(converterAttr.ConverterType);

                if (converter is JsonConverter jsonConverter)
                {
                    property.Converter = jsonConverter;
                }
            }
            catch
            {
                // Ignore converter creation errors
            }
        }
    }
}
