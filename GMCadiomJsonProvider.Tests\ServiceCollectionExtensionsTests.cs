namespace GMCadiomJsonProvider.Tests;

public class ServiceCollectionExtensionsTests
{
    [Fact]
    public void AddJsonProviderFactory_ShouldRegisterFactory()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddJsonProviderFactory();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var factory = serviceProvider.GetService<IJsonProviderFactory>();
        factory.Should().NotBeNull();
    }

    [Fact]
    public void AddJsonProvider_WithoutParameters_ShouldRegisterMicrosoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddJsonProvider();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddJsonProvider_WithConfiguration_ShouldRegisterSpecifiedProvider()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddJsonProvider(config => config.ProviderType = JsonProviderType.Newtonsoft);
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddMicrosoftJsonProvider_WithoutConfiguration_ShouldRegisterMicrosoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddMicrosoftJsonProvider();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void AddMicrosoftJsonProvider_WithConfiguration_ShouldApplyOptions()
    {
        // Arrange
        var services = new ServiceCollection();
        var testPerson = new TestPerson { Id = 1, Name = "John Doe" };

        // Act
        services.AddMicrosoftJsonProvider(config =>
        {
            config.WriteIndented = true;
        });
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();

        var json = provider!.Serialize(testPerson);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void AddNewtonsoftJsonProvider_WithoutConfiguration_ShouldRegisterNewtonsoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddNewtonsoftJsonProvider();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        provider.ProviderName.Should().Be("Newtonsoft");
    }

    [Fact]
    public void AddNewtonsoftJsonProvider_WithConfiguration_ShouldApplyOptions()
    {
        // Arrange
        var services = new ServiceCollection();
        var testPerson = new TestPerson { Id = 1, Name = "John Doe" };

        // Act
        services.AddNewtonsoftJsonProvider(config =>
        {
            config.WriteIndented = true;
        });
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();

        var json = provider!.Serialize(testPerson);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void AddJsonProvider_WithBuilder_ShouldConfigureProvider()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddJsonProvider(builder =>
        {
            builder.UseNewtonsoft()
                   .WithIndentation(true);
        });
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddJsonProvider_WithUnifiedConfiguration_ShouldUseSpecifiedConfiguration()
    {
        // Arrange
        var services = new ServiceCollection();
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };

        // Act
        services.AddJsonProvider(config);
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddJsonProvider_WithNewtonsoftConfiguration_ShouldUseNewtonsoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };

        // Act
        services.AddJsonProvider(config);
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider = serviceProvider.GetService<IJsonProvider>();
        provider.Should().NotBeNull();
        provider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void MultipleRegistrations_ShouldUseSingletonPattern()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddJsonProvider();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var provider1 = serviceProvider.GetService<IJsonProvider>();
        var provider2 = serviceProvider.GetService<IJsonProvider>();

        provider1.Should().BeSameAs(provider2);
    }
}
