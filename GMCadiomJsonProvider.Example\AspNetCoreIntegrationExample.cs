namespace GMCadiomJsonProvider.Example;

/// <summary>
/// Example demonstrating ASP.NET Core integration with GMCadiomJsonProvider.
/// </summary>
public class AspNetCoreIntegrationExample
{
    /// <summary>
    /// Example of configuring a web application with GMCadiomJsonProvider.
    /// </summary>
    public static void ConfigureWebApplication()
    {
        Console.WriteLine("ASP.NET Core Integration Examples");
        Console.WriteLine("=================================\n");

        // Example 1: Basic WebApplicationBuilder integration
        Console.WriteLine("1. Basic WebApplicationBuilder Integration:");
        Console.WriteLine("==========================================");

        var builder = WebApplication.CreateBuilder();

        // Add GMCadiomJsonProvider with default Microsoft provider
        builder.AddGMCadiomJson(config =>
        {
            config.WriteIndented = true;
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            config.NullValueHandling = JsonNullValueHandling.Ignore;
        });

        Console.WriteLine("✓ Added GMCadiomJsonProvider to WebApplicationBuilder with Microsoft provider");

        // Example 2: MVC Integration
        Console.WriteLine("\n2. MVC Integration:");
        Console.WriteLine("==================");

        builder.Services.AddControllers()
            .AddGMCadiomNewtonsoftJson(config =>
            {
                config.WriteIndented = false; // Compact for APIs
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
                config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
            });

        Console.WriteLine("✓ Configured MVC with GMCadiomJsonProvider using Newtonsoft provider");

        // Example 3: SignalR Integration
        Console.WriteLine("\n3. SignalR Integration:");
        Console.WriteLine("======================");

        builder.Services.AddSignalR()
            .AddGMCadiomMicrosoftJsonProtocol(config =>
            {
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.WriteIndented = false;
                config.PropertyNameCaseInsensitive = true;
            });

        Console.WriteLine("✓ Configured SignalR with GMCadiomJsonProvider using Microsoft provider");

        // Example 4: HTTP Client Integration
        Console.WriteLine("\n4. HTTP Client Integration:");
        Console.WriteLine("===========================");

        builder.Services.AddHttpClient("ApiClient")
            .AddGMCadiomJson(config =>
            {
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
            });

        Console.WriteLine("✓ Configured HTTP Client with GMCadiomJsonProvider");

        // Example 5: Using Builder Pattern
        Console.WriteLine("\n5. Using Builder Pattern:");
        Console.WriteLine("=========================");

        builder.Services.AddControllers()
            .AddGMCadiomJson(jsonBuilder =>
            {
                jsonBuilder.UseMicrosoft()
                          .WithIndentation(false)
                          .WithCaseInsensitiveProperties(true)
                          .WithIgnoreNullValues(true)
                          .ConfigureOptions(config =>
                          {
                              config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                              config.MaxDepth = 32;
                          });
            });

        Console.WriteLine("✓ Configured MVC using fluent builder pattern");

        // Example 6: JSON Configuration
        Console.WriteLine("\n6. JSON Configuration:");
        Console.WriteLine("=====================");

        var jsonConfig = """
        {
            "providerType": "Microsoft",
            "writeIndented": false,
            "propertyNamingPolicy": "CamelCase",
            "nullValueHandling": "Ignore",
            "propertyNameCaseInsensitive": true,
            "maxDepth": 64
        }
        """;

        builder.Services.AddSignalR()
            .AddGMCadiomJsonProtocolFromConfig(jsonConfig);

        Console.WriteLine("✓ Configured SignalR from JSON configuration");

        var app = builder.Build();

        // Configure the HTTP request pipeline
        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseRouting();
        app.MapControllers();
        app.MapHub<ExampleHub>("/exampleHub");

        Console.WriteLine("\n✓ Web application configured successfully!");
        Console.WriteLine("  - Controllers configured with GMCadiomJsonProvider");
        Console.WriteLine("  - SignalR hub configured with GMCadiomJsonProvider");
        Console.WriteLine("  - HTTP clients configured with GMCadiomJsonProvider");
    }

    /// <summary>
    /// Example of advanced configuration scenarios.
    /// </summary>
    public static void AdvancedConfigurationExamples()
    {
        Console.WriteLine("\n\nAdvanced Configuration Examples");
        Console.WriteLine("===============================\n");

        var builder = WebApplication.CreateBuilder();

        // Example 1: Different providers for different services
        Console.WriteLine("1. Different Providers for Different Services:");
        Console.WriteLine("==============================================");

        // Use Microsoft provider for MVC (faster)
        builder.Services.AddControllers()
            .AddGMCadiomMicrosoftJson(config =>
            {
                config.WriteIndented = false;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            });

        // Use Newtonsoft for SignalR (more features)
        builder.Services.AddSignalR()
            .AddGMCadiomNewtonsoftJsonProtocol(config =>
            {
                config.WriteIndented = false;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
            });

        Console.WriteLine("✓ MVC configured with Microsoft provider");
        Console.WriteLine("✓ SignalR configured with Newtonsoft provider");

        // Example 2: Environment-specific configuration
        Console.WriteLine("\n2. Environment-Specific Configuration:");
        Console.WriteLine("=====================================");

        if (builder.Environment.IsDevelopment())
        {
            builder.AddGMCadiomJson(config =>
            {
                config.WriteIndented = true; // Pretty print in development
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            });
            Console.WriteLine("✓ Development: Pretty-printed JSON enabled");
        }
        else
        {
            builder.AddGMCadiomJson(config =>
            {
                config.WriteIndented = false; // Compact in production
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
            });
            Console.WriteLine("✓ Production: Compact JSON with null value ignoring");
        }

        // Example 3: Multiple HTTP clients with different configurations
        Console.WriteLine("\n3. Multiple HTTP Clients:");
        Console.WriteLine("=========================");

        // Internal API client - compact JSON
        builder.Services.AddHttpClient("InternalApi")
            .AddGMCadiomMicrosoftJson(config =>
            {
                config.WriteIndented = false;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            });

        // External API client - more tolerant configuration
        builder.Services.AddHttpClient("ExternalApi")
            .AddGMCadiomNewtonsoftJson(config =>
            {
                config.PropertyNameCaseInsensitive = true;
                config.MissingMemberHandling = JsonMissingMemberHandling.Ignore;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            });

        Console.WriteLine("✓ InternalApi client: Microsoft provider with strict settings");
        Console.WriteLine("✓ ExternalApi client: Newtonsoft provider with tolerant settings");

        Console.WriteLine("\n✓ Advanced configuration completed!");
    }
}

/// <summary>
/// Example SignalR hub for demonstration.
/// </summary>
public class ExampleHub : Hub
{
    public async Task SendMessage(string user, string message)
    {
        await Clients.All.SendAsync("ReceiveMessage", user, message);
    }
}
