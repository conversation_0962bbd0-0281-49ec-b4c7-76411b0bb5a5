namespace GMCadiomJsonProvider.Builder;

/// <summary>
/// Defines the contract for building JSON providers with fluent configuration.
/// </summary>
public interface IJsonProviderBuilder
{
    /// <summary>
    /// Configures the builder to use Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseMicrosoft(Action<JsonConfiguration>? configure = null);

    /// <summary>
    /// Configures the builder to use Newtonsoft.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseNewtonsoft(Action<JsonConfiguration>? configure = null);

    /// <summary>
    /// Configures the builder to use a specific provider type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to use.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseProvider(JsonProviderType providerType);

    /// <summary>
    /// Configures common JSON options.
    /// </summary>
    /// <param name="configure">Action to configure common options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder ConfigureOptions(Action<JsonConfiguration> configure);

    /// <summary>
    /// Sets whether to write indented JSON.
    /// </summary>
    /// <param name="writeIndented">True to write indented JSON; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithIndentation(bool writeIndented = true);

    /// <summary>
    /// Sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    /// <param name="caseInsensitive">True for case-insensitive property names; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithCaseInsensitiveProperties(bool caseInsensitive = true);

    /// <summary>
    /// Sets whether to allow trailing commas in JSON.
    /// </summary>
    /// <param name="allowTrailingCommas">True to allow trailing commas; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithTrailingCommas(bool allowTrailingCommas = true);

    /// <summary>
    /// Sets whether to ignore null values during serialization.
    /// </summary>
    /// <param name="ignoreNullValues">True to ignore null values; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithIgnoreNullValues(bool ignoreNullValues = true);

    /// <summary>
    /// Configures the builder using a unified configuration.
    /// </summary>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseConfiguration(JsonConfiguration config);

    /// <summary>
    /// Configures the builder using a unified configuration action.
    /// </summary>
    /// <param name="configure">Action to configure the unified JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseConfiguration(Action<JsonConfiguration> configure);

    /// <summary>
    /// Configures the builder from a JSON configuration string.
    /// </summary>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseJsonConfiguration(string jsonConfig);

    /// <summary>
    /// Enables or disables processing of custom GMCadiom JSON attributes.
    /// </summary>
    /// <param name="enabled">True to enable custom attribute processing; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithCustomAttributes(bool enabled = true);

    /// <summary>
    /// Specifies the types to process custom attributes for.
    /// </summary>
    /// <param name="types">The types to process. If null, all types with custom attributes are processed.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithCustomAttributeTypes(IEnumerable<Type>? types);

    /// <summary>
    /// Specifies the types to process custom attributes for.
    /// </summary>
    /// <param name="types">The types to process.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithCustomAttributeTypes(params Type[] types);

    /// <summary>
    /// Builds the configured JSON provider.
    /// </summary>
    /// <returns>A configured JSON provider instance.</returns>
    IJsonProvider Build();
}
