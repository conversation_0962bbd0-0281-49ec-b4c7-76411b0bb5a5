namespace GMCadiomJsonProvider.Attributes;

/// <summary>
/// Specifies that a property should be included in JSON serialization.
/// This attribute works with both Microsoft System.Text.Json and Newtonsoft.Json providers.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
public sealed class GMCadiomJsonIncludeAttribute : Attribute
{
    /// <summary>
    /// Gets or sets the condition under which the property should be included.
    /// </summary>
    public GMCadiomJsonIncludeCondition Condition { get; set; } = GMCadiomJsonIncludeCondition.Always;

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonIncludeAttribute"/> class.
    /// </summary>
    public GMCadiomJsonIncludeAttribute()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonIncludeAttribute"/> class with the specified condition.
    /// </summary>
    /// <param name="condition">The condition under which the property should be included.</param>
    public GMCadiomJsonIncludeAttribute(GMCadiomJsonIncludeCondition condition)
    {
        Condition = condition;
    }
}

/// <summary>
/// Defines the conditions under which a property should be included in JSON serialization.
/// </summary>
public enum GMCadiomJsonIncludeCondition
{
    /// <summary>
    /// The property is always included.
    /// </summary>
    Always,

    /// <summary>
    /// The property is included only when it's not null.
    /// </summary>
    WhenNotNull,

    /// <summary>
    /// The property is included only when it's not the default value.
    /// </summary>
    WhenNotDefault,

    /// <summary>
    /// The property is never included.
    /// </summary>
    Never
}
