namespace GMCadiomJsonProvider.Attributes;

/// <summary>
/// Specifies that a constructor should be used for JSON deserialization.
/// This attribute works with both Microsoft System.Text.Json and Newtonsoft.Json providers.
/// </summary>
[AttributeUsage(AttributeTargets.Constructor, AllowMultiple = false)]
public sealed class GMCadiomJsonConstructorAttribute : Attribute
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonConstructorAttribute"/> class.
    /// </summary>
    public GMCadiomJsonConstructorAttribute()
    {
    }
}
