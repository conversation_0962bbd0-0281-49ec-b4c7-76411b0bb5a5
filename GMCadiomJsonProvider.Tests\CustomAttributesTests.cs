using FluentAssertions;
using GMCadiomJsonProvider.Attributes;
using Xunit;

namespace GMCadiomJsonProvider.Tests;

/// <summary>
/// Tests for custom GMCadiom JSON attributes functionality.
/// </summary>
public class CustomAttributesTests
{
    [Fact]
    public void CustomAttributes_WithMicrosoftProvider_ShouldRespectAttributes()
    {
        // Arrange
        var provider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(TestPersonWithAttributes))
            .Build();

        var person = new TestPersonWithAttributes
        {
            Id = 123,
            Name = "John <PERSON>e",
            Email = "<EMAIL>",
            SecretData = "secret",
            OptionalField = null
        };

        // Act
        var json = provider.Serialize(person);
        var deserialized = provider.Deserialize<TestPersonWithAttributes>(json);

        // Assert
        json.Should().Contain("\"custom_name\""); // Property name attribute
        json.Should().Contain("\"email_address\""); // Property name attribute
        json.Should().NotContain("SecretData"); // Should be ignored
        json.Should().NotContain("OptionalField"); // Should be ignored when null
        
        deserialized.Should().NotBeNull();
        deserialized!.Id.Should().Be(123);
        deserialized.Name.Should().Be("John Doe");
        deserialized.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public void CustomAttributes_WithNewtonsoftProvider_ShouldRespectAttributes()
    {
        // Arrange
        var provider = GMCadiomJson.CreateBuilder()
            .UseNewtonsoft()
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(TestPersonWithAttributes))
            .Build();

        var person = new TestPersonWithAttributes
        {
            Id = 123,
            Name = "John Doe",
            Email = "<EMAIL>",
            SecretData = "secret",
            OptionalField = null
        };

        // Act
        var json = provider.Serialize(person);
        var deserialized = provider.Deserialize<TestPersonWithAttributes>(json);

        // Assert
        json.Should().Contain("\"custom_name\""); // Property name attribute
        json.Should().Contain("\"email_address\""); // Property name attribute
        json.Should().NotContain("SecretData"); // Should be ignored
        json.Should().NotContain("OptionalField"); // Should be ignored when null
        
        deserialized.Should().NotBeNull();
        deserialized!.Id.Should().Be(123);
        deserialized.Name.Should().Be("John Doe");
        deserialized.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public void CustomAttributes_WhenDisabled_ShouldIgnoreAttributes()
    {
        // Arrange
        var provider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithCustomAttributes(false) // Disabled
            .Build();

        var person = new TestPersonWithAttributes
        {
            Id = 123,
            Name = "John Doe",
            Email = "<EMAIL>",
            SecretData = "secret",
            OptionalField = "optional"
        };

        // Act
        var json = provider.Serialize(person);

        // Assert
        json.Should().Contain("\"Name\""); // Original property name
        json.Should().Contain("\"Email\""); // Original property name
        json.Should().Contain("\"SecretData\""); // Should not be ignored
        json.Should().NotContain("\"custom_name\""); // Custom name should not be used
        json.Should().NotContain("\"email_address\""); // Custom name should not be used
    }

    [Fact]
    public void JsonIncludeAttribute_WithWhenNotNull_ShouldIncludeOnlyNonNullValues()
    {
        // Arrange
        var provider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(TestPersonWithAttributes))
            .Build();

        var personWithNull = new TestPersonWithAttributes
        {
            Id = 123,
            Name = "John Doe",
            Email = null, // This should be excluded
            OptionalField = null // This should be excluded
        };

        var personWithValue = new TestPersonWithAttributes
        {
            Id = 123,
            Name = "John Doe",
            Email = "<EMAIL>", // This should be included
            OptionalField = "optional" // This should be included
        };

        // Act
        var jsonWithNull = provider.Serialize(personWithNull);
        var jsonWithValue = provider.Serialize(personWithValue);

        // Assert
        jsonWithNull.Should().NotContain("email_address");
        jsonWithNull.Should().NotContain("OptionalField");
        
        jsonWithValue.Should().Contain("email_address");
        jsonWithValue.Should().Contain("OptionalField");
    }

    [Fact]
    public void JsonConstructorAttribute_ShouldUseMarkedConstructor()
    {
        // Arrange
        var provider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(TestPersonWithConstructor))
            .Build();

        var person = new TestPersonWithConstructor("John", "Doe", 30);
        var json = provider.Serialize(person);

        // Act
        var deserialized = provider.Deserialize<TestPersonWithConstructor>(json);

        // Assert
        deserialized.Should().NotBeNull();
        deserialized!.FirstName.Should().Be("John");
        deserialized.LastName.Should().Be("Doe");
        deserialized.Age.Should().Be(30);
    }
}

/// <summary>
/// Test class with various custom attributes.
/// </summary>
public class TestPersonWithAttributes
{
    public int Id { get; set; }

    [GMCadiomJsonPropertyName("custom_name")]
    public string Name { get; set; } = string.Empty;

    [GMCadiomJsonPropertyName("email_address")]
    [GMCadiomJsonInclude(GMCadiomJsonIncludeCondition.WhenNotNull)]
    public string? Email { get; set; }

    [GMCadiomJsonIgnore(GMCadiomJsonIgnoreCondition.Always)]
    public string SecretData { get; set; } = string.Empty;

    [GMCadiomJsonInclude(GMCadiomJsonIncludeCondition.WhenNotNull)]
    public string? OptionalField { get; set; }
}

/// <summary>
/// Test class with custom constructor attribute.
/// </summary>
public class TestPersonWithConstructor
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public int Age { get; set; }

    public TestPersonWithConstructor()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
    }

    [GMCadiomJsonConstructor]
    public TestPersonWithConstructor(string firstName, string lastName, int age)
    {
        FirstName = firstName;
        LastName = lastName;
        Age = age;
    }
}
