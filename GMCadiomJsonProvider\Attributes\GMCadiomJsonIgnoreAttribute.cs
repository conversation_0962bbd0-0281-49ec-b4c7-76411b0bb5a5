namespace GMCadiomJsonProvider.Attributes;

/// <summary>
/// Specifies that a property should be ignored during JSON serialization and deserialization.
/// This attribute works with both Microsoft System.Text.Json and Newtonsoft.Json providers.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
public sealed class GMCadiomJsonIgnoreAttribute : Attribute
{
    /// <summary>
    /// Gets or sets the condition under which the property should be ignored.
    /// </summary>
    public GMCadiomJsonIgnoreCondition Condition { get; set; } = GMCadiomJsonIgnoreCondition.Always;

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonIgnoreAttribute"/> class.
    /// </summary>
    public GMCadiomJsonIgnoreAttribute()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonIgnoreAttribute"/> class with the specified condition.
    /// </summary>
    /// <param name="condition">The condition under which the property should be ignored.</param>
    public GMCadiomJsonIgnoreAttribute(GMCadiomJsonIgnoreCondition condition)
    {
        Condition = condition;
    }
}

/// <summary>
/// Defines the conditions under which a property should be ignored during JSON serialization.
/// </summary>
public enum GMCadiomJsonIgnoreCondition
{
    /// <summary>
    /// The property is always ignored.
    /// </summary>
    Always,

    /// <summary>
    /// The property is ignored only when writing (serialization).
    /// </summary>
    WhenWriting,

    /// <summary>
    /// The property is ignored only when reading (deserialization).
    /// </summary>
    WhenReading,

    /// <summary>
    /// The property is ignored when the value is null.
    /// </summary>
    WhenNull,

    /// <summary>
    /// The property is ignored when the value is the default value.
    /// </summary>
    WhenDefault,

    /// <summary>
    /// The property is never ignored.
    /// </summary>
    Never
}
