namespace GMCadiomJsonProvider.Example;

/// <summary>
/// Example demonstrating SignalR client integration with GMCadiomJsonProvider.
/// </summary>
public class SignalRClientExample
{
    /// <summary>
    /// Example of configuring SignalR client connections with GMCadiomJsonProvider.
    /// </summary>
    public static async Task RunClientExamples()
    {
        Console.WriteLine("SignalR Client Integration Examples");
        Console.WriteLine("===================================\n");

        // Example 1: Basic client connection with Microsoft provider
        Console.WriteLine("1. Basic SignalR Client with Microsoft Provider:");
        Console.WriteLine("================================================");

        var connection1 = new HubConnectionBuilder()
            .WithUrl("https://example.com/chatHub")
            .AddGMCadiomJsonProtocol(config =>
            {
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.WriteIndented = false;
                config.PropertyNameCaseInsensitive = true;
            })
            .Build();

        Console.WriteLine("✓ Created SignalR client connection with Microsoft JSON provider");
        Console.WriteLine($"  Connection State: {connection1.State}");

        // Example 2: Client connection with Newtonsoft provider
        Console.WriteLine("\n2. SignalR Client with Newtonsoft Provider:");
        Console.WriteLine("===========================================");

        var connection2 = new HubConnectionBuilder()
            .WithUrl("https://example.com/chatHub")
            .AddGMCadiomNewtonsoftJsonProtocol(config =>
            {
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
            })
            .Build();

        Console.WriteLine("✓ Created SignalR client connection with Newtonsoft JSON provider");
        Console.WriteLine($"  Connection State: {connection2.State}");

        // Example 3: Using fluent builder pattern
        Console.WriteLine("\n3. SignalR Client with Fluent Builder:");
        Console.WriteLine("======================================");

        var connection3 = new HubConnectionBuilder()
            .WithUrl("https://example.com/chatHub")
            .AddGMCadiomJsonProtocol(jsonBuilder =>
            {
                jsonBuilder.UseMicrosoft()
                          .WithIndentation(false)
                          .WithCaseInsensitiveProperties(true)
                          .WithIgnoreNullValues(true)
                          .ConfigureOptions(config =>
                          {
                              config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                              config.MaxDepth = 32;
                          });
            })
            .Build();

        Console.WriteLine("✓ Created SignalR client connection using fluent builder pattern");
        Console.WriteLine($"  Connection State: {connection3.State}");

        // Example 4: Using JSON configuration
        Console.WriteLine("\n4. SignalR Client from JSON Configuration:");
        Console.WriteLine("==========================================");

        var jsonConfig = """
        {
            "providerType": "Microsoft",
            "writeIndented": false,
            "propertyNamingPolicy": "CamelCase",
            "nullValueHandling": "Ignore",
            "propertyNameCaseInsensitive": true,
            "maxDepth": 64
        }
        """;

        var connection4 = new HubConnectionBuilder()
            .WithUrl("https://example.com/chatHub")
            .AddGMCadiomJsonProtocolFromConfig(jsonConfig)
            .Build();

        Console.WriteLine("✓ Created SignalR client connection from JSON configuration");
        Console.WriteLine($"  Connection State: {connection4.State}");

        // Example 5: Different configurations for different hubs
        Console.WriteLine("\n5. Different Configurations for Different Hubs:");
        Console.WriteLine("===============================================");

        // Chat hub - compact JSON for performance
        var chatConnection = new HubConnectionBuilder()
            .WithUrl("https://example.com/chatHub")
            .AddGMCadiomMicrosoftJsonProtocol(config =>
            {
                config.WriteIndented = false;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
            })
            .Build();

        // Notification hub - more tolerant configuration
        var notificationConnection = new HubConnectionBuilder()
            .WithUrl("https://example.com/notificationHub")
            .AddGMCadiomNewtonsoftJsonProtocol(config =>
            {
                config.PropertyNameCaseInsensitive = true;
                config.MissingMemberHandling = JsonMissingMemberHandling.Ignore;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
            })
            .Build();

        Console.WriteLine("✓ Chat Hub: Microsoft provider with compact settings");
        Console.WriteLine("✓ Notification Hub: Newtonsoft provider with tolerant settings");

        // Example 6: Event handlers and message sending
        Console.WriteLine("\n6. Event Handlers and Message Sending:");
        Console.WriteLine("======================================");

        var demoConnection = new HubConnectionBuilder()
            .WithUrl("https://example.com/demoHub")
            .AddGMCadiomJsonProtocol(config =>
            {
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.WriteIndented = false;
            })
            .Build();

        // Set up event handlers
        demoConnection.On<string, string>("ReceiveMessage", (user, message) =>
        {
            Console.WriteLine($"  📨 Received: {user}: {message}");
        });

        demoConnection.On<ChatMessage>("ReceiveChatMessage", (chatMessage) =>
        {
            Console.WriteLine($"  📨 Received chat: {chatMessage.User}: {chatMessage.Message} at {chatMessage.Timestamp}");
        });

        Console.WriteLine("✓ Event handlers configured");
        Console.WriteLine("✓ Ready to send/receive messages with JSON serialization");

        // Cleanup
        await connection1.DisposeAsync();
        await connection2.DisposeAsync();
        await connection3.DisposeAsync();
        await connection4.DisposeAsync();
        await chatConnection.DisposeAsync();
        await notificationConnection.DisposeAsync();
        await demoConnection.DisposeAsync();

        Console.WriteLine("\n✓ All SignalR client connections disposed");
        Console.WriteLine("\n✓ SignalR Client Integration examples completed!");
    }

    /// <summary>
    /// Demonstrates a complete SignalR client scenario with JSON serialization.
    /// </summary>
    public static async Task RunCompleteClientScenario()
    {
        Console.WriteLine("\n\nComplete SignalR Client Scenario");
        Console.WriteLine("=================================\n");

        try
        {
            // Create connection with GMCadiomJsonProvider
            var connection = new HubConnectionBuilder()
                .WithUrl("https://localhost:5001/chatHub") // Example URL
                .AddGMCadiomJsonProtocol(config =>
                {
                    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                    config.WriteIndented = false;
                    config.PropertyNameCaseInsensitive = true;
                })
                .Build();

            Console.WriteLine("✓ SignalR connection created with GMCadiomJsonProvider");

            // Set up message handlers
            connection.On<ChatMessage>("ReceiveMessage", (message) =>
            {
                Console.WriteLine($"📨 {message.User}: {message.Message}");
            });

            connection.On<string>("UserJoined", (user) =>
            {
                Console.WriteLine($"👋 {user} joined the chat");
            });

            connection.On<string>("UserLeft", (user) =>
            {
                Console.WriteLine($"👋 {user} left the chat");
            });

            Console.WriteLine("✓ Event handlers configured");

            // Note: In a real scenario, you would start the connection here
            // await connection.StartAsync();

            // Simulate sending messages (would work if connected to a real hub)
            var sampleMessage = new ChatMessage
            {
                User = "TestUser",
                Message = "Hello from GMCadiomJsonProvider!",
                Timestamp = DateTime.UtcNow
            };

            Console.WriteLine($"📤 Would send: {sampleMessage.User}: {sampleMessage.Message}");
            // await connection.InvokeAsync("SendMessage", sampleMessage);

            Console.WriteLine("✓ Message sending demonstrated");

            // Cleanup
            await connection.DisposeAsync();
            Console.WriteLine("✓ Connection disposed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in SignalR client scenario: {ex.Message}");
            Console.WriteLine("   (This is expected since we're not connecting to a real hub)");
        }

        Console.WriteLine("\n✓ Complete SignalR client scenario finished!");
    }
}

/// <summary>
/// Example chat message model for SignalR communication.
/// </summary>
public class ChatMessage
{
    public string User { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? Channel { get; set; }
    public MessageType Type { get; set; } = MessageType.Text;
}

/// <summary>
/// Example message type enumeration.
/// </summary>
public enum MessageType
{
    Text,
    Image,
    File,
    System
}
