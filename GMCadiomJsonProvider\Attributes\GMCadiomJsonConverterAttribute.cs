namespace GMCadiomJsonProvider.Attributes;

/// <summary>
/// Specifies a custom converter to use for JSON serialization and deserialization.
/// This attribute works with both Microsoft System.Text.Json and Newtonsoft.Json providers.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Interface, AllowMultiple = false)]
public sealed class GMCadiomJsonConverterAttribute : Attribute
{
    /// <summary>
    /// Gets the converter type.
    /// </summary>
    public Type ConverterType { get; }

    /// <summary>
    /// Gets the converter parameters.
    /// </summary>
    public object[]? Parameters { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonConverterAttribute"/> class.
    /// </summary>
    /// <param name="converterType">The type of the converter to use.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="converterType"/> is null.</exception>
    public GMCadiomJsonConverterAttribute(Type converterType)
    {
        ConverterType = converterType ?? throw new ArgumentNullException(nameof(converterType));
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonConverterAttribute"/> class with parameters.
    /// </summary>
    /// <param name="converterType">The type of the converter to use.</param>
    /// <param name="parameters">The parameters to pass to the converter constructor.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="converterType"/> is null.</exception>
    public GMCadiomJsonConverterAttribute(Type converterType, params object[] parameters)
    {
        ConverterType = converterType ?? throw new ArgumentNullException(nameof(converterType));
        Parameters = parameters;
    }
}
