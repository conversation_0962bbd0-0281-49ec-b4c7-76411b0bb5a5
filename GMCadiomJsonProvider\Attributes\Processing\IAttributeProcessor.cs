namespace GMCadiomJsonProvider.Attributes.Processing;

/// <summary>
/// Defines the contract for processing custom JSON attributes.
/// </summary>
public interface IAttributeProcessor
{
    /// <summary>
    /// Processes custom attributes and configures System.Text.Json options accordingly.
    /// </summary>
    /// <param name="options">The JsonSerializerOptions to configure.</param>
    /// <param name="types">The types to process attributes for. If null, processes all loaded types.</param>
    void ProcessAttributesForSystemTextJson(System.Text.Json.JsonSerializerOptions options, IEnumerable<Type>? types = null);

    /// <summary>
    /// Processes custom attributes and configures Newtonsoft.Json settings accordingly.
    /// </summary>
    /// <param name="settings">The JsonSerializerSettings to configure.</param>
    /// <param name="types">The types to process attributes for. If null, processes all loaded types.</param>
    void ProcessAttributesForNewtonsoft(Newtonsoft.Json.JsonSerializerSettings settings, IEnumerable<Type>? types = null);

    /// <summary>
    /// Gets all types that have custom JSON attributes applied.
    /// </summary>
    /// <returns>An enumerable of types with custom attributes.</returns>
    IEnumerable<Type> GetTypesWithCustomAttributes();

    /// <summary>
    /// Checks if a type has any custom JSON attributes.
    /// </summary>
    /// <param name="type">The type to check.</param>
    /// <returns>True if the type has custom attributes; otherwise, false.</returns>
    bool HasCustomAttributes(Type type);
}
