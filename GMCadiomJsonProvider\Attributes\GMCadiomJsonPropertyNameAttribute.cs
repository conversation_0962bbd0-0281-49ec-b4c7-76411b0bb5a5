namespace GMCadiomJsonProvider.Attributes;

/// <summary>
/// Specifies the JSON property name for a property during serialization and deserialization.
/// This attribute works with both Microsoft System.Text.Json and Newtonsoft.Json providers.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
public sealed class GMCadiomJsonPropertyNameAttribute : Attribute
{
    /// <summary>
    /// Gets the JSON property name.
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="GMCadiomJsonPropertyNameAttribute"/> class.
    /// </summary>
    /// <param name="name">The JSON property name.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="name"/> is null.</exception>
    /// <exception cref="ArgumentException">Thrown when <paramref name="name"/> is empty or whitespace.</exception>
    public GMCadiomJsonPropertyNameAttribute(string name)
    {
        if (name == null)
            throw new ArgumentNullException(nameof(name));
        
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Property name cannot be empty or whitespace.", nameof(name));

        Name = name;
    }
}
