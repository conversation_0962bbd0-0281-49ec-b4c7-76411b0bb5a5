namespace GMCadiomJsonProvider.Tests;

public class SystemTextJsonProviderTests
{
    private readonly TestPerson _testPerson;

    public SystemTextJsonProviderTests()
    {
        _testPerson = new TestPerson
        {
            Id = 1,
            Name = "<PERSON>",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true,
            Salary = 50000.50m,
            Tags = new List<string> { "developer", "senior" },
            Address = new TestAddress
            {
                Street = "123 Main St",
                City = "New York",
                Country = "USA",
                PostalCode = "10001"
            }
        };
    }

    [Fact]
    public void Constructor_WithDefaultOptions_ShouldCreateProvider()
    {
        // Act
        var provider = new SystemTextJsonProvider();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void Constructor_WithCustomConfiguration_ShouldCreateProvider()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };

        // Act
        var provider = new SystemTextJsonProvider(config);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void Serialize_WithValidObject_ShouldReturnJsonString()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();

        // Act
        var json = provider.Serialize(_testPerson);

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("John Doe");
        json.Should().Contain("<EMAIL>");
    }

    [Fact]
    public void Serialize_WithIndentedConfiguration_ShouldReturnFormattedJson()
    {
        // Arrange
        var config = new JsonConfiguration { ProviderType = JsonProviderType.Microsoft, WriteIndented = true };
        var provider = new SystemTextJsonProvider(config);

        // Act
        var json = provider.Serialize(_testPerson);

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("\n");
        json.Should().Contain("  ");
    }

    [Fact]
    public void Deserialize_WithValidJson_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        var json = provider.Serialize(_testPerson);

        // Act
        var result = provider.Deserialize<TestPerson>(json);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(_testPerson.Id);
        result.Name.Should().Be(_testPerson.Name);
        result.Email.Should().Be(_testPerson.Email);
        result.IsActive.Should().Be(_testPerson.IsActive);
        result.Address.Should().NotBeNull();
        result.Address!.City.Should().Be(_testPerson.Address!.City);
    }

    [Fact]
    public async Task SerializeAsync_WithValidObject_ShouldReturnJsonString()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();

        // Act
        var json = await provider.SerializeAsync(_testPerson);

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("John Doe");
    }

    [Fact]
    public async Task DeserializeAsync_WithValidJson_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        var json = await provider.SerializeAsync(_testPerson);

        // Act
        var result = await provider.DeserializeAsync<TestPerson>(json);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(_testPerson.Name);
    }

    [Fact]
    public void SerializeToStream_WithValidObject_ShouldWriteToStream()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        using var stream = new MemoryStream();

        // Act
        provider.SerializeToStream(stream, _testPerson);

        // Assert
        stream.Length.Should().BeGreaterThan(0);
        stream.Position = 0;
        using var reader = new StreamReader(stream);
        var json = reader.ReadToEnd();
        json.Should().Contain("John Doe");
    }

    [Fact]
    public void DeserializeFromStream_WithValidStream_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        using var stream = new MemoryStream();
        provider.SerializeToStream(stream, _testPerson);
        stream.Position = 0;

        // Act
        var result = provider.DeserializeFromStream<TestPerson>(stream);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(_testPerson.Name);
    }

    [Fact]
    public async Task SerializeToStreamAsync_WithValidObject_ShouldWriteToStream()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        using var stream = new MemoryStream();

        // Act
        await provider.SerializeToStreamAsync(stream, _testPerson);

        // Assert
        stream.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task DeserializeFromStreamAsync_WithValidStream_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        using var stream = new MemoryStream();
        await provider.SerializeToStreamAsync(stream, _testPerson);
        stream.Position = 0;

        // Act
        var result = await provider.DeserializeFromStreamAsync<TestPerson>(stream);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(_testPerson.Name);
    }

    [Fact]
    public void Deserialize_WithType_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        var json = provider.Serialize(_testPerson);

        // Act
        var result = provider.Deserialize(json, typeof(TestPerson));

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<TestPerson>();
        ((TestPerson)result!).Name.Should().Be(_testPerson.Name);
    }

    [Fact]
    public void DeserializeFromStream_WithType_ShouldReturnObject()
    {
        // Arrange
        var provider = new SystemTextJsonProvider();
        using var stream = new MemoryStream();
        provider.SerializeToStream(stream, _testPerson);
        stream.Position = 0;

        // Act
        var result = provider.DeserializeFromStream(stream, typeof(TestPerson));

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<TestPerson>();
        ((TestPerson)result!).Name.Should().Be(_testPerson.Name);
    }
}
