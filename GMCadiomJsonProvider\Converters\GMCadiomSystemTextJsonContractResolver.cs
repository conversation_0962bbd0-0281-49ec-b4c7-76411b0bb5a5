using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using GMCadiomJsonProvider.Attributes;

namespace GMCadiomJsonProvider.Converters;

/// <summary>
/// Custom contract resolver for System.Text.Json that processes GMCadiom custom attributes.
/// </summary>
public class GMCadiomSystemTextJsonContractResolver : DefaultJsonTypeInfoResolver
{
    private readonly HashSet<Type> _typesWithCustomAttributes = new();
    private readonly object _lock = new();

    /// <summary>
    /// Adds a type that has custom attributes to be processed.
    /// </summary>
    /// <param name="type">The type with custom attributes.</param>
    public void AddTypeWithCustomAttributes(Type type)
    {
        lock (_lock)
        {
            _typesWithCustomAttributes.Add(type);
        }
    }

    /// <summary>
    /// Gets the type information for the specified type.
    /// </summary>
    /// <param name="type">The type to get information for.</param>
    /// <param name="options">The JsonSerializerOptions.</param>
    /// <returns>The JsonTypeInfo for the type.</returns>
    public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
    {
        var typeInfo = base.GetTypeInfo(type, options);

        lock (_lock)
        {
            if (_typesWithCustomAttributes.Contains(type))
            {
                ProcessCustomAttributes(typeInfo, type);
            }
        }

        return typeInfo;
    }

    private void ProcessCustomAttributes(JsonTypeInfo typeInfo, Type type)
    {
        // Process constructor attributes
        ProcessConstructorAttributes(typeInfo, type);

        // Process property attributes
        ProcessPropertyAttributes(typeInfo, type);
    }

    private void ProcessConstructorAttributes(JsonTypeInfo typeInfo, Type type)
    {
        var constructors = type.GetConstructors();
        var markedConstructor = constructors.FirstOrDefault(c => 
            c.GetCustomAttribute<GMCadiomJsonConstructorAttribute>() != null);

        if (markedConstructor != null && typeInfo.CreateObject == null)
        {
            // Set the constructor to use for deserialization
            var parameters = markedConstructor.GetParameters();
            if (parameters.Length == 0)
            {
                typeInfo.CreateObject = () => Activator.CreateInstance(type);
            }
            else
            {
                // For parameterized constructors, we need to create a custom factory
                typeInfo.CreateObject = () => CreateObjectWithConstructor(markedConstructor, typeInfo);
            }
        }
    }

    private void ProcessPropertyAttributes(JsonTypeInfo typeInfo, Type type)
    {
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            ProcessMemberAttributes(typeInfo, property);
        }

        foreach (var field in fields)
        {
            ProcessMemberAttributes(typeInfo, field);
        }
    }

    private void ProcessMemberAttributes(JsonTypeInfo typeInfo, MemberInfo member)
    {
        var propertyInfo = typeInfo.Properties.FirstOrDefault(p => p.Name == member.Name);
        if (propertyInfo == null) return;

        // Process GMCadiomJsonPropertyName attribute
        var propertyNameAttr = member.GetCustomAttribute<GMCadiomJsonPropertyNameAttribute>();
        if (propertyNameAttr != null)
        {
            propertyInfo.Name = propertyNameAttr.Name;
        }

        // Process GMCadiomJsonIgnore attribute
        var ignoreAttr = member.GetCustomAttribute<GMCadiomJsonIgnoreAttribute>();
        if (ignoreAttr != null)
        {
            switch (ignoreAttr.Condition)
            {
                case GMCadiomJsonIgnoreCondition.Always:
                    propertyInfo.ShouldSerialize = (_, _) => false;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenWriting:
                    propertyInfo.ShouldSerialize = (_, _) => false;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenReading:
                    propertyInfo.Set = null;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenNull:
                    propertyInfo.ShouldSerialize = (obj, value) => value != null;
                    break;
                case GMCadiomJsonIgnoreCondition.WhenDefault:
                    propertyInfo.ShouldSerialize = (obj, value) => !IsDefaultValue(value, member);
                    break;
            }
        }

        // Process GMCadiomJsonInclude attribute
        var includeAttr = member.GetCustomAttribute<GMCadiomJsonIncludeAttribute>();
        if (includeAttr != null)
        {
            switch (includeAttr.Condition)
            {
                case GMCadiomJsonIncludeCondition.Always:
                    propertyInfo.ShouldSerialize = (_, _) => true;
                    break;
                case GMCadiomJsonIncludeCondition.WhenNotNull:
                    propertyInfo.ShouldSerialize = (obj, value) => value != null;
                    break;
                case GMCadiomJsonIncludeCondition.WhenNotDefault:
                    propertyInfo.ShouldSerialize = (obj, value) => !IsDefaultValue(value, member);
                    break;
                case GMCadiomJsonIncludeCondition.Never:
                    propertyInfo.ShouldSerialize = (_, _) => false;
                    break;
            }
        }
    }

    private static object CreateObjectWithConstructor(ConstructorInfo constructor, JsonTypeInfo typeInfo)
    {
        var parameters = constructor.GetParameters();
        var args = new object[parameters.Length];

        for (int i = 0; i < parameters.Length; i++)
        {
            var paramType = parameters[i].ParameterType;
            args[i] = paramType.IsValueType ? Activator.CreateInstance(paramType) : null;
        }

        return constructor.Invoke(args);
    }

    private static bool IsDefaultValue(object? value, MemberInfo member)
    {
        if (value == null) return true;

        var memberType = member switch
        {
            PropertyInfo prop => prop.PropertyType,
            FieldInfo field => field.FieldType,
            _ => typeof(object)
        };

        if (memberType.IsValueType)
        {
            var defaultValue = Activator.CreateInstance(memberType);
            return value.Equals(defaultValue);
        }

        return false;
    }
}
