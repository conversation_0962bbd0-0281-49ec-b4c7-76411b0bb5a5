namespace GMCadiomJsonProvider.Tests;

public class JsonConfigurationTests
{
    private readonly TestPerson _testPerson;

    public JsonConfigurationTests()
    {
        _testPerson = new TestPerson
        {
            Id = 1,
            Name = "<PERSON>",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true,
            Salary = null // Test null handling
        };
    }

    [Fact]
    public void JsonConfiguration_DefaultValues_ShouldBeCorrect()
    {
        // Act
        var config = new JsonConfiguration();

        // Assert
        config.ProviderType.Should().Be(JsonProviderType.Microsoft);
        config.WriteIndented.Should().BeFalse();
        config.PropertyNameCaseInsensitive.Should().BeFalse();
        config.AllowTrailingCommas.Should().BeFalse();
        config.NullValueHandling.Should().Be(JsonNullValueHandling.Include);
        config.PropertyNamingPolicy.Should().Be(JsonNamingPolicy.Default);
        config.MaxDepth.Should().Be(64);
    }

    [Fact]
    public void JsonConfiguration_Clone_ShouldCreateExactCopy()
    {
        // Arrange
        var original = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            NullValueHandling = JsonNullValueHandling.Ignore,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            MaxDepth = 128
        };

        // Act
        var clone = original.Clone();

        // Assert
        clone.Should().NotBeSameAs(original);
        clone.ProviderType.Should().Be(original.ProviderType);
        clone.WriteIndented.Should().Be(original.WriteIndented);
        clone.PropertyNameCaseInsensitive.Should().Be(original.PropertyNameCaseInsensitive);
        clone.NullValueHandling.Should().Be(original.NullValueHandling);
        clone.PropertyNamingPolicy.Should().Be(original.PropertyNamingPolicy);
        clone.MaxDepth.Should().Be(original.MaxDepth);
    }

    [Fact]
    public void JsonConfiguration_ToJson_ShouldSerializeCorrectly()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Act
        var json = config.ToJson();

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("\"providerType\": \"Newtonsoft\"");
        json.Should().Contain("\"writeIndented\": true");
        json.Should().Contain("\"propertyNamingPolicy\": \"CamelCase\"");
    }

    [Fact]
    public void JsonConfiguration_FromJson_ShouldDeserializeCorrectly()
    {
        // Arrange
        var json = """
        {
            "providerType": "Newtonsoft",
            "writeIndented": true,
            "propertyNamingPolicy": "CamelCase",
            "nullValueHandling": "Ignore",
            "maxDepth": 128
        }
        """;

        // Act
        var config = JsonConfiguration.FromJson(json);

        // Assert
        config.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        config.WriteIndented.Should().BeTrue();
        config.PropertyNamingPolicy.Should().Be(JsonNamingPolicy.CamelCase);
        config.NullValueHandling.Should().Be(JsonNullValueHandling.Ignore);
        config.MaxDepth.Should().Be(128);
    }

    [Fact]
    public void JsonConfigurationMapper_ToSystemTextJsonOptions_ShouldMapCorrectly()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            AllowTrailingCommas = true,
            NullValueHandling = JsonNullValueHandling.Ignore,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            IncludeFields = true,
            MaxDepth = 128,
            AllowComments = true,
            AllowNumbersFromStrings = true,
            UseRelaxedEscaping = true
        };

        // Act
        var options = config.ToSystemTextJsonOptions();

        // Assert
        options.WriteIndented.Should().BeTrue();
        options.PropertyNameCaseInsensitive.Should().BeTrue();
        options.AllowTrailingCommas.Should().BeTrue();
        options.DefaultIgnoreCondition.Should().Be(System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull);
        options.PropertyNamingPolicy.Should().Be(System.Text.Json.JsonNamingPolicy.CamelCase);
        options.IncludeFields.Should().BeTrue();
        options.MaxDepth.Should().Be(128);
        options.ReadCommentHandling.Should().Be(System.Text.Json.JsonCommentHandling.Skip);
        options.NumberHandling.Should().Be(System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString);
        options.Encoder.Should().Be(System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping);
    }

    [Fact]
    public void JsonConfigurationMapper_ToNewtonsoftJsonOptions_ShouldMapCorrectly()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            AllowTrailingCommas = true,
            NullValueHandling = JsonNullValueHandling.Ignore,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DateFormatHandling = JsonDateFormatHandling.IsoDateFormat,
            MissingMemberHandling = JsonMissingMemberHandling.Error,
            ReferenceLoopHandling = JsonReferenceLoopHandling.Ignore,
            MaxDepth = 128,
            Culture = "en-US"
        };

        // Act
        var settings = config.ToNewtonsoftJsonSettings();

        // Assert
        settings.Formatting.Should().Be(Newtonsoft.Json.Formatting.Indented);
        settings.NullValueHandling.Should().Be(Newtonsoft.Json.NullValueHandling.Ignore);
        settings.ContractResolver.Should().BeOfType<Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver>();
        settings.DateFormatHandling.Should().Be(Newtonsoft.Json.DateFormatHandling.IsoDateFormat);
        settings.MissingMemberHandling.Should().Be(Newtonsoft.Json.MissingMemberHandling.Error);
        settings.ReferenceLoopHandling.Should().Be(Newtonsoft.Json.ReferenceLoopHandling.Ignore);
        settings.MaxDepth.Should().Be(128);
        settings.Culture?.Name.Should().Be("en-US");
    }



    [Fact]
    public void UnifiedConfiguration_WithMicrosoftProvider_ShouldWorkCorrectly()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore
        };

        // Act
        var provider = GMCadiomJson.CreateProvider(config);
        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void UnifiedConfiguration_WithNewtonsoftProvider_ShouldWorkCorrectly()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore
        };

        // Act
        var provider = GMCadiomJson.CreateProvider(config);
        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void UnifiedConfiguration_WithAction_ShouldWorkCorrectly()
    {
        // Act
        var provider = GMCadiomJson.CreateProvider(config =>
        {
            config.ProviderType = JsonProviderType.Newtonsoft;
            config.WriteIndented = true;
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            config.NullValueHandling = JsonNullValueHandling.Ignore;
        });

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }
}
