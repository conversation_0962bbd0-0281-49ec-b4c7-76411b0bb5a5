using GMCadiomJsonProvider;
using GMCadiomJsonProvider.Attributes;

namespace GMCadiomJsonProvider.Example;

/// <summary>
/// Example demonstrating the use of custom GMCadiom JSON attributes.
/// </summary>
public static class CustomAttributesExample
{
    public static void Run()
    {
        Console.WriteLine("GMCadiom JSON Provider - Custom Attributes Example");
        Console.WriteLine("==================================================\n");

        var person = new PersonWithAttributes
        {
            Id = 123,
            FullName = "John Doe",
            Email = "<EMAIL>",
            Age = 30,
            InternalId = "INTERNAL_12345",
            SecretData = "This should be ignored",
            OptionalField = null
        };

        Console.WriteLine("1. Using Microsoft Provider with Custom Attributes:");
        Console.WriteLine("==================================================");

        var microsoftProvider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithIndentation(true)
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(PersonWithAttributes))
            .Build();

        var microsoftJson = microsoftProvider.Serialize(person);
        Console.WriteLine($"Serialized JSON:\n{microsoftJson}\n");

        var deserializedMicrosoft = microsoftProvider.Deserialize<PersonWithAttributes>(microsoftJson);
        Console.WriteLine($"Deserialized: {deserializedMicrosoft}\n");

        Console.WriteLine("2. Using Newtonsoft Provider with Custom Attributes:");
        Console.WriteLine("===================================================");

        var newtonsoftProvider = GMCadiomJson.CreateBuilder()
            .UseNewtonsoft()
            .WithIndentation(true)
            .WithCustomAttributes(true)
            .WithCustomAttributeTypes(typeof(PersonWithAttributes))
            .Build();

        var newtonsoftJson = newtonsoftProvider.Serialize(person);
        Console.WriteLine($"Serialized JSON:\n{newtonsoftJson}\n");

        var deserializedNewtonsoft = newtonsoftProvider.Deserialize<PersonWithAttributes>(newtonsoftJson);
        Console.WriteLine($"Deserialized: {deserializedNewtonsoft}\n");

        Console.WriteLine("3. Comparison without Custom Attributes:");
        Console.WriteLine("=======================================");

        var standardProvider = GMCadiomJson.CreateBuilder()
            .UseMicrosoft()
            .WithIndentation(true)
            .WithCustomAttributes(false) // Disable custom attributes
            .Build();

        var standardJson = standardProvider.Serialize(person);
        Console.WriteLine($"Standard JSON (without custom attributes):\n{standardJson}\n");

        Console.WriteLine("4. Using Custom Constructor:");
        Console.WriteLine("============================");

        var constructorExample = new PersonWithConstructor("Jane", "Smith", 25);
        var constructorJson = microsoftProvider.Serialize(constructorExample);
        Console.WriteLine($"Serialized with constructor:\n{constructorJson}\n");

        var deserializedConstructor = microsoftProvider.Deserialize<PersonWithConstructor>(constructorJson);
        Console.WriteLine($"Deserialized with constructor: {deserializedConstructor}\n");
    }
}

/// <summary>
/// Example class demonstrating various custom JSON attributes.
/// </summary>
public class PersonWithAttributes
{
    [GMCadiomJsonInclude(GMCadiomJsonIncludeCondition.Always)]
    public int Id { get; set; }

    [GMCadiomJsonPropertyName("full_name")]
    public string FullName { get; set; } = string.Empty;

    [GMCadiomJsonPropertyName("email_address")]
    [GMCadiomJsonInclude(GMCadiomJsonIncludeCondition.WhenNotNull)]
    public string? Email { get; set; }

    public int Age { get; set; }

    [GMCadiomJsonIgnore(GMCadiomJsonIgnoreCondition.Always)]
    public string InternalId { get; set; } = string.Empty;

    [GMCadiomJsonIgnore(GMCadiomJsonIgnoreCondition.WhenWriting)]
    public string SecretData { get; set; } = string.Empty;

    [GMCadiomJsonInclude(GMCadiomJsonIncludeCondition.WhenNotNull)]
    public string? OptionalField { get; set; }

    public override string ToString()
    {
        return $"Person {{ Id: {Id}, FullName: '{FullName}', Email: '{Email}', Age: {Age}, InternalId: '{InternalId}', SecretData: '{SecretData}', OptionalField: '{OptionalField}' }}";
    }
}

/// <summary>
/// Example class demonstrating custom constructor attribute.
/// </summary>
public class PersonWithConstructor
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public int Age { get; set; }

    // Default constructor for serialization
    public PersonWithConstructor()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
    }

    // Custom constructor marked for JSON deserialization
    [GMCadiomJsonConstructor]
    public PersonWithConstructor(string firstName, string lastName, int age)
    {
        FirstName = firstName;
        LastName = lastName;
        Age = age;
    }

    public override string ToString()
    {
        return $"PersonWithConstructor {{ FirstName: '{FirstName}', LastName: '{LastName}', Age: {Age} }}";
    }
}
