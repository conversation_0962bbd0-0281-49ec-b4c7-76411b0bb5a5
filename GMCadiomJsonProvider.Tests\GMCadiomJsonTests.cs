namespace GMCadiomJsonProvider.Tests;

public class GMCadiomJsonTests
{
    private readonly TestPerson _testPerson;

    public GMCadiomJsonTests()
    {
        _testPerson = new TestPerson
        {
            Id = 1,
            Name = "<PERSON>",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true
        };
    }

    [Fact]
    public void Factory_ShouldReturnFactoryInstance()
    {
        // Act
        var factory = GMCadiomJson.Factory;

        // Assert
        factory.Should().NotBeNull();
    }

    [Fact]
    public void Default_ShouldReturnMicrosoftProvider()
    {
        // Act
        var provider = GMCadiomJson.Default;

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void CreateBuilder_ShouldReturnBuilderInstance()
    {
        // Act
        var builder = GMCadiomJson.CreateBuilder();

        // Assert
        builder.Should().NotBeNull();
    }

    [Fact]
    public void CreateDefault_ShouldReturnMicrosoftProvider()
    {
        // Act
        var provider = GMCadiomJson.CreateDefault();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void CreateMicrosoft_WithoutConfiguration_ShouldReturnMicrosoftProvider()
    {
        // Act
        var provider = GMCadiomJson.CreateMicrosoft();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void CreateMicrosoft_WithConfiguration_ShouldApplyOptions()
    {
        // Act
        var provider = GMCadiomJson.CreateMicrosoft(options =>
        {
            options.WriteIndented = true;
        });

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void CreateNewtonsoft_WithoutConfiguration_ShouldReturnNewtonsoftProvider()
    {
        // Act
        var provider = GMCadiomJson.CreateNewtonsoft();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        provider.ProviderName.Should().Be("Newtonsoft");
    }

    [Fact]
    public void CreateNewtonsoft_WithConfiguration_ShouldApplyOptions()
    {
        // Act
        var provider = GMCadiomJson.CreateNewtonsoft(options =>
        {
            options.WriteIndented = true;
        });

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void CreateProvider_WithMicrosoft_ShouldReturnMicrosoftProvider()
    {
        // Act
        var provider = GMCadiomJson.CreateProvider(JsonProviderType.Microsoft);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void CreateProvider_WithNewtonsoft_ShouldReturnNewtonsoftProvider()
    {
        // Act
        var provider = GMCadiomJson.CreateProvider(JsonProviderType.Newtonsoft);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void Serialize_WithDefaultProvider_ShouldReturnJsonString()
    {
        // Act
        var json = GMCadiomJson.Serialize(_testPerson);

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("John Doe");
        json.Should().Contain("<EMAIL>");
    }

    [Fact]
    public async Task SerializeAsync_WithDefaultProvider_ShouldReturnJsonString()
    {
        // Act
        var json = await GMCadiomJson.SerializeAsync(_testPerson);

        // Assert
        json.Should().NotBeNullOrEmpty();
        json.Should().Contain("John Doe");
    }

    [Fact]
    public void Deserialize_WithDefaultProvider_ShouldReturnObject()
    {
        // Arrange
        var json = GMCadiomJson.Serialize(_testPerson);

        // Act
        var result = GMCadiomJson.Deserialize<TestPerson>(json);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(_testPerson.Name);
        result.Email.Should().Be(_testPerson.Email);
    }

    [Fact]
    public async Task DeserializeAsync_WithDefaultProvider_ShouldReturnObject()
    {
        // Arrange
        var json = await GMCadiomJson.SerializeAsync(_testPerson);

        // Act
        var result = await GMCadiomJson.DeserializeAsync<TestPerson>(json);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(_testPerson.Name);
    }

    [Fact]
    public void Deserialize_WithType_ShouldReturnObject()
    {
        // Arrange
        var json = GMCadiomJson.Serialize(_testPerson);

        // Act
        var result = GMCadiomJson.Deserialize(json, typeof(TestPerson));

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<TestPerson>();
        ((TestPerson)result!).Name.Should().Be(_testPerson.Name);
    }
}
