namespace GMCadiomJsonProvider;

/// <summary>
/// Static helper class for creating and using JSON providers.
/// </summary>
public static class GMCadiomJson
{
    private static readonly Lazy<IJsonProviderFactory> _factory = new(() => new JsonProviderFactory());
    private static readonly Lazy<IJsonProvider> _defaultProvider = new(() => CreateDefault());

    /// <summary>
    /// Gets the default JSON provider factory.
    /// </summary>
    public static IJsonProviderFactory Factory => _factory.Value;

    /// <summary>
    /// Gets the default JSON provider (Microsoft System.Text.Json).
    /// </summary>
    public static IJsonProvider Default => _defaultProvider.Value;

    /// <summary>
    /// Creates a new JSON provider builder for fluent configuration.
    /// </summary>
    /// <returns>A new JSON provider builder instance.</returns>
    public static IJsonProviderBuilder CreateBuilder()
    {
        return new JsonProviderBuilder();
    }

    /// <summary>
    /// Creates a default JSON provider (Microsoft System.Text.Json).
    /// </summary>
    /// <returns>A default JSON provider instance.</returns>
    public static IJsonProvider CreateDefault()
    {
        return new SystemTextJsonProvider();
    }

    /// <summary>
    /// Creates a Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>A Microsoft JSON provider instance.</returns>
    public static IJsonProvider CreateMicrosoft(Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = JsonProviderType.Microsoft };
        configure?.Invoke(config);
        return new SystemTextJsonProvider(config);
    }

    /// <summary>
    /// Creates a Newtonsoft.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>A Newtonsoft JSON provider instance.</returns>
    public static IJsonProvider CreateNewtonsoft(Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = JsonProviderType.Newtonsoft };
        configure?.Invoke(config);
        return new NewtonsoftJsonProvider(config);
    }

    /// <summary>
    /// Creates a JSON provider of the specified type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to create.</param>
    /// <returns>A JSON provider instance.</returns>
    public static IJsonProvider CreateProvider(JsonProviderType providerType)
    {
        return Factory.CreateProvider(providerType);
    }

    /// <summary>
    /// Creates a JSON provider using unified configuration.
    /// </summary>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>A JSON provider instance.</returns>
    public static IJsonProvider CreateProvider(JsonConfiguration config)
    {
        return Factory.CreateProvider(config);
    }

    /// <summary>
    /// Creates a JSON provider using unified configuration action.
    /// </summary>
    /// <param name="configure">Action to configure the unified JSON configuration.</param>
    /// <returns>A JSON provider instance.</returns>
    public static IJsonProvider CreateProvider(Action<JsonConfiguration> configure)
    {
        var config = new JsonConfiguration();
        configure(config);
        return Factory.CreateProvider(config);
    }

    /// <summary>
    /// Creates a JSON provider from a JSON configuration string.
    /// </summary>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>A JSON provider instance.</returns>
    public static IJsonProvider CreateProviderFromJson(string jsonConfig)
    {
        return Factory.CreateProviderFromJson(jsonConfig);
    }

    /// <summary>
    /// Serializes an object to a JSON string using the default provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <returns>A JSON string representation of the object.</returns>
    public static string Serialize<T>(T value)
    {
        return Default.Serialize(value);
    }

    /// <summary>
    /// Serializes an object to a JSON string asynchronously using the default provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a JSON string representation of the object.</returns>
    public static Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default)
    {
        return Default.SerializeAsync(value, cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type using the default provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <returns>The deserialized object.</returns>
    public static T? Deserialize<T>(string json)
    {
        return Default.Deserialize<T>(json);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type asynchronously using the default provider.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    public static Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default)
    {
        return Default.DeserializeAsync<T>(json, cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type using the default provider.
    /// </summary>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    public static object? Deserialize(string json, Type type)
    {
        return Default.Deserialize(json, type);
    }
}
